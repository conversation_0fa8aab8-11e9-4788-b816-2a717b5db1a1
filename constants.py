# pylint: skip-file
# flake8: noqa
# DuckDB local verion geo-api-sot
# 20250630 1148 Added daily_impressions from mortalportal.fact_all_panels_impressions_daily
# 20250618 1527 Changed order of operations - cap by population first, then by reach
# 20250515 1648 Implemented FUA-based population cap on reach
# 20250515 1217 Switched to using temp_tables.panel_id_pairwise_lower_reach_panel_dedupe_rate_5min
# 20250512 1229 Uses temp_tables.panel_id_pairwise_lower_reach_panel_dedupe_rate3
# 20250430 1655 Implemented new panel_id_dow_contacts_cumulative table
# 20250424 0937 Switched from two lookups to one lookup for panel_id_pairwise
# 20250423 1043 Added panel_id_overlap table
# 20250411 0943 Added nearby_panel_pairs_with_mixture_factor
# 20250410 1214 Added count_of_weeks_in_campaign
# 20250409 1126 Added first_week_index to campaign_panels_with_dates
# 20250325 1250 Switched to using daily_reach from panel_id_base_reach
# 20250319 1257 Added lookup_overall_dedupe_rate
# 20250307 0932 Swapped <PERSON><PERSON><PERSON>'s panel_trend_updated table for models.seasonal_scalers_by_date
# 20250305 1254 Added panel count check cte and combined rows as final select
# 20250227 1017 new deployment
# 20250226 1310 Implemented new build curves table and using temp_tables.panel_id_scaled_dow_means_by_date_with_copies3 for reach
# 20250226 1028 Switch from calibre.dim_panel2 to mortalportal.dim_panel_vc_inc
# 20250217 1557 Added check for missing panel ids
# 20250214 1328 Forcing top ranked panel to 1 for overall dedupe rate
# 20250214 1254 Added nearby panel dedupe scaler
# 20250212 0936 Changed to FUA demographics
# 20250212 0851 campaign_panels_with_dates needs to be forced to be UNIQUE by panel_id and local_date
# 20250211 1306 Adding in road dedupe scaler
# 20250210 1003 Added cluster_dedupe_minimum table
# 20250207 1538 Copy-paste from geo-api-local
# 20250205 1409 Back to 0.55 for overall dedupe rate
# 20250205 0939 Added tags
# 20250131 1309 Replaced road groups with maid overlap groups
# 20250130 0951 Add raod group deduplication
# _x_ TODO: Include Vishal's new build curves tables
# 20250123 1154 Added seasonal_scalers_by_date table with seasonal_share_of_max_adjusted column
# 20250123 1402 Added COALESCE on join of dedupe_curves table to cover missing dedupe_rates (19th asset rank and higher)

SQL_CALIBRE_HEAD = """
-- SQL_CALIBRE_HEAD
-- STEP: Get all panel_ids and local_dates for the campaign via temp_tables.{table}
--       Converting selected from_date and to_date to weeks within the interval
WITH campaign_panels_with_dates_raw AS ( -- tag: cte
    SELECT DISTINCT -- DISTINCT here prevents double booking of a panel on the exact same date
        panel_id, 
        local_date,
        days
    FROM (
        SELECT
            panel_id, 
            -- d AS local_date,
            d['unnest'] AS local_date,
            -- LEAST(date_diff(DATE(to_date), DATE(from_date), DAY) + 1, 7) AS days
            -- Modified for DuckDB
            LEAST(date_diff('day', local_date, cast(to_date as date)) + 1, 7) AS days
        FROM
            temp_tables.{table} as panels, -- tag: table
            -- UNNEST(GENERATE_DATE_ARRAY(DATE(from_date), DATE(to_date), INTERVAL 1 WEEK)) d, 
            UNNEST(generate_series(strptime(from_date, '%Y-%m-%d'), strptime(to_date, '%Y-%m-%d'), INTERVAL 1 WEEK)) as d
    )
    ORDER BY panel_id, local_date
), 

campaign_panels_with_build_days AS ( -- tag: cte
    SELECT
        cpr.*,
        bd.build_days
    FROM
        campaign_panels_with_dates_raw as cpr
    JOIN
        models.build_days as bd
    ON
        cpr.days = bd.days
),

campaign_panels_with_day_of_week AS ( -- tag: cte
    SELECT 
        panel_id, 
        cp.local_date,
        days,
        build_days,
        week_index, -- has integer values from 0 for week of 2024-01-01 on through 313 for week of 2029-12-31
        -- EXTRACT(DAYOFWEEK FROM cp.local_date) as day_of_week_number
        -- Modified for DuckDB
        CASE WHEN EXTRACT(DAYOFWEEK FROM cp.local_date) = 0 THEN 7 ELSE EXTRACT(DAYOFWEEK FROM cp.local_date) END as day_of_week_number
    FROM 
        campaign_panels_with_build_days as cp
    JOIN
        temp_tables.local_date_in_week as ldiw -- tag: table
    ON
        -- cp.local_date = DATE(ldiw.local_date)
        cp.local_date = CAST(ldiw.local_date as date)
    ORDER BY 
        panel_id, 
        cp.local_date
),

panel_weeks AS ( -- tag: cte
    SELECT DISTINCT
        panel_id,
        week_index
    FROM campaign_panels_with_day_of_week
),

count_of_weeks_in_campaign AS ( -- tag: cte
    SELECT
    1 as id,
    count(DISTINCT week_index) as weeks_in_campaign
    FROM panel_weeks
),

panel_weeks_with_week_number AS ( -- tag: cte
    SELECT
        panel_id,
        week_index,
        ROW_NUMBER() OVER (PARTITION BY panel_id ORDER BY week_index) as week
    FROM panel_weeks
),

panel_first_week_index AS ( -- tag: cte
    SELECT
        panel_id,
        MIN(week_index) as first_week_index
    FROM panel_weeks_with_week_number
    GROUP BY panel_id
),

campaign_panels_with_dates AS ( -- tag: cte
    SELECT
        cpdw.panel_id, 
        cpdw.local_date,
        cpdw.days,
        CASE WHEN pw.week = 1 THEN cpdw.build_days ELSE cpdw.days END as build_days,
        cpdw.week_index,
        cpdw.day_of_week_number,
        pfwi.first_week_index,
        pw.week
    FROM
        campaign_panels_with_day_of_week as cpdw
    JOIN
        panel_first_week_index as pfwi
    ON
        cpdw.panel_id = pfwi.panel_id
    JOIN
        panel_weeks_with_week_number as pw
    ON
        cpdw.panel_id = pw.panel_id AND
        cpdw.week_index = pw.week_index
),
-- Example of campaign_panels_with_dates data:
-- panel_id	local_date	days	build_days week_index	day_of_week_number	first_week_index	week
-- 6222     2024-03-29   1          2.8       12               5                12                1

-- STEP: Count panels in. Should match panels with reach out later.
panels_in_count AS ( -- tag: cte
    SELECT
        1 as id,
        COUNT(DISTINCT panel_id) as number_of_panels_in
    FROM
        campaign_panels_with_dates),
"""

SQL_REACH_BODY = """
-- SQL_REACH_BODY
-- Pre-requisite: campaign_panels_with_dates

-- STEP: Look up individual panel attributes and join with campaign dates and build curves
panel_reach AS ( -- tag: cte
    SELECT
        cpd.panel_id,
        cpd.local_date,
        cpd.days,
        cpd.build_days,
        cpd.day_of_week_number,
        cpd.first_week_index,
        cpd.week,
        dp.product,
        dp.digital_static,
        pct.curve_type,
        bc.build_curve_rate,
        pdc.dow_contacts_cumulative,
        EXTRACT(WEEK FROM cpd.local_date) - 1 as extracted_week_from_local_date, -- -1 is a DuckDB adjustment DO NOT COMMIT
        st.trend,
        pr.daily_reach,
        bc.build_curve_rate * cpd.build_days * pr.daily_reach * st.trend AS total_reach,
        CASE WHEN dp.product = 'Placebased' THEN bc.build_curve_rate * cpd.build_days * pr.daily_reach * st.trend ELSE 0 END AS placebased_reach,
        CASE WHEN dp.product = 'Roadside' THEN bc.build_curve_rate * cpd.build_days * pr.daily_reach * st.trend ELSE 0 END AS roadside_reach,
        CASE WHEN dp.digital_static = 'Digital' THEN bc.build_curve_rate * cpd.build_days * pr.daily_reach * st.trend ELSE 0 END AS digital_reach,
        CASE WHEN dp.digital_static = 'Static' THEN bc.build_curve_rate * cpd.build_days * pr.daily_reach * st.trend ELSE 0 END AS static_reach,
        pdc.dow_contacts_cumulative AS total_contacts_pre_trend,
        pdc.dow_contacts_cumulative * st.trend AS total_contacts,
        CASE WHEN dp.product = 'Placebased' THEN pdc.dow_contacts_cumulative * st.trend ELSE 0 END AS placebased_contacts,
        CASE WHEN dp.product = 'Roadside' THEN pdc.dow_contacts_cumulative * st.trend ELSE 0 END AS roadside_contacts,
        CASE WHEN dp.digital_static = 'Digital' THEN pdc.dow_contacts_cumulative * st.trend ELSE 0 END AS digital_contacts,
        CASE WHEN dp.digital_static = 'Static' THEN pdc.dow_contacts_cumulative * st.trend ELSE 0 END AS static_contacts,
        -- Share of Time (SOT) Reach
        COALESCE(pim.daily_reach_as_SOT, pr.daily_reach) as daily_reach_as_sot,
        bc.build_curve_rate * cpd.build_days * COALESCE(pim.daily_reach_as_SOT, pr.daily_reach) * st.trend AS total_reach_sot,
        CASE WHEN dp.product = 'Placebased' THEN bc.build_curve_rate * cpd.build_days * COALESCE(pim.daily_reach_as_SOT, pr.daily_reach) * st.trend ELSE 0 END AS placebased_reach_sot,
        CASE WHEN dp.product = 'Roadside' THEN bc.build_curve_rate * cpd.build_days * COALESCE(pim.daily_reach_as_SOT, pr.daily_reach) * st.trend ELSE 0 END AS roadside_reach_sot,
        CASE WHEN dp.digital_static = 'Digital' THEN bc.build_curve_rate * cpd.build_days * COALESCE(pim.daily_reach_as_SOT, pr.daily_reach) * st.trend ELSE 0 END AS digital_reach_sot,
        CASE WHEN dp.digital_static = 'Static' THEN bc.build_curve_rate * cpd.build_days * COALESCE(pim.daily_reach_as_SOT, pr.daily_reach) * st.trend ELSE 0 END AS static_reach_sot,        
        -- Share of Time (SOT) Impressions
        pim.daily_impressions AS daily_impressions_pre_trend,
        0.15 as impressions_factor, -- converts from 100 percent impressions to single ad impressions at 15 percent
        0.15 * COALESCE(cpd.build_days * pim.daily_impressions, pdc.dow_contacts_cumulative) * st.trend AS impressions,
        0.15 * CASE WHEN dp.product = 'Placebased' THEN COALESCE(cpd.build_days * pim.daily_impressions, pdc.dow_contacts_cumulative) * st.trend ELSE 0 END AS placebased_impressions,
        0.15 * CASE WHEN dp.product = 'Roadside' THEN COALESCE(cpd.build_days * pim.daily_impressions, pdc.dow_contacts_cumulative) * st.trend ELSE 0 END AS roadside_impressions,
        0.15 * CASE WHEN dp.digital_static = 'Digital' THEN COALESCE(cpd.build_days * pim.daily_impressions, pdc.dow_contacts_cumulative) * st.trend ELSE 0 END AS digital_impressions,
        0.15 * CASE WHEN dp.digital_static = 'Static' THEN COALESCE(cpd.build_days * pim.daily_impressions, pdc.dow_contacts_cumulative) * st.trend ELSE 0 END AS static_impressions        
    FROM
        campaign_panels_with_dates as cpd
    INNER JOIN 
        mortalportal.dim_panel_vc_inc as dp -- tag: table
        ON dp.panel_id = cpd.panel_id AND dp.latest = True
    INNER JOIN 
        temp_tables.panel_id_curve_type as pct -- tag: table
        ON pct.panel_id = cpd.panel_id
    INNER JOIN 
        models.build_curves_20250415 as bc -- tag: table
        ON bc.curve_type = pct.curve_type AND bc.week = cpd.week
    INNER JOIN 
        temp_tables.panel_id_dow_contacts_cumulative_20250603 as pdc -- tag: table
        ON pdc.panel_id = cpd.panel_id 
        AND pdc.dow_number = cpd.day_of_week_number
        AND pdc.day_count = cpd.days
    INNER JOIN 
        temp_tables.panel_id_base_reach as pr -- tag: table
        ON pr.panel_id = cpd.panel_id
        AND pr.dow_number = cpd.day_of_week_number
    INNER JOIN 
        mortalportal.panels_trend_updated as st -- tag: table
        ON st.week = EXTRACT(WEEK FROM cpd.local_date) - 1  -- -1 is a DuckDB adjustment DO NOT COMMIT
        AND st.panel_id = cpd.panel_id
    LEFT JOIN
        mortalportal.fact_all_panels_impressions_daily as pim -- tag: table
        ON pim.panel_id = cpd.panel_id
        AND pim.weekday = cpd.day_of_week_number
    ORDER BY
        cpd.panel_id,
        cpd.local_date
),

-- STEP: Calculate sum of weekly panel reach (collapse dates)
sum_of_weekly_reach_by_panel AS ( -- tag: cte
    SELECT 
        panel_id,
        MIN(first_week_index) as first_week_index,
        SUM(total_reach) AS total_reach,
        SUM(placebased_reach) AS placebased_reach,
        SUM(roadside_reach) AS roadside_reach,
        SUM(digital_reach) AS digital_reach,
        SUM(static_reach) AS static_reach,

        SUM(total_contacts) AS total_contacts,
        SUM(placebased_contacts) AS placebased_contacts,
        SUM(roadside_contacts) AS roadside_contacts,
        SUM(digital_contacts) AS digital_contacts,
        SUM(static_contacts) AS static_contacts,
        
        -- Share of Time (SOT) Reach
        SUM(total_reach_sot) AS total_reach_sot,
        SUM(placebased_reach_sot) AS placebased_reach_sot,
        SUM(roadside_reach_sot) AS roadside_reach_sot,
        SUM(digital_reach_sot) AS digital_reach_sot,
        SUM(static_reach_sot) AS static_reach_sot,
        
        -- Share of Time (SOT) Impressions
        SUM(impressions) AS impressions,
        SUM(placebased_impressions) AS placebased_impressions,
        SUM(roadside_impressions) AS roadside_impressions,
        SUM(digital_impressions) AS digital_impressions,
        SUM(static_impressions) AS static_impressions
    FROM 
        panel_reach
    GROUP BY 
        panel_id
),

panels_out_count AS ( -- tag: cte
    SELECT
        1 as id,
        COUNT(DISTINCT panel_id) as number_of_panels_out,
        (SELECT STRING_AGG(CAST(cpwd.panel_id AS VARCHAR), ', ')
         FROM (SELECT DISTINCT panel_id FROM campaign_panels_with_dates) cpwd
         WHERE NOT EXISTS (
             SELECT 1 FROM sum_of_weekly_reach_by_panel swrp 
             WHERE swrp.panel_id = cpwd.panel_id
         )) as missing_panel_ids,
    FROM
        sum_of_weekly_reach_by_panel
),
"""

SQL_CALIBRE_FINAL = """
-- SQL_CALIBRE_FINAL
-- Pre-requisite: SQL_REACH_BODY

-- STEP: Look up demographic data and fa shares by panel_id
-- Source: temp_tables.panel_id_demographics_and_fas
reach_with_panel_demographics AS ( -- tag: cte
    SELECT 
        swrp.panel_id,
        swrp.first_week_index,
        swrp.total_reach,
        swrp.placebased_reach,
        swrp.roadside_reach,
        swrp.digital_reach,
        swrp.static_reach,

        -- GREATEST here prevents contacts being less than reach
        GREATEST(swrp.total_contacts, swrp.total_reach) as total_contacts,
        GREATEST(swrp.placebased_contacts, swrp.placebased_reach) as placebased_contacts,
        GREATEST(swrp.roadside_contacts, swrp.roadside_reach) as roadside_contacts,
        GREATEST(swrp.digital_contacts, swrp.digital_reach) as digital_contacts,
        GREATEST(swrp.static_contacts, swrp.static_reach) as static_contacts,
        
        -- Share of Time (SOT) Reach
        swrp.total_reach_sot,
        swrp.placebased_reach_sot,
        swrp.roadside_reach_sot,
        swrp.digital_reach_sot,
        swrp.static_reach_sot,
        
        -- Share of Time (SOT) Impressions
        swrp.impressions,
        swrp.placebased_impressions,
        swrp.roadside_impressions,
        swrp.digital_impressions,
        swrp.static_impressions,
        
        -- These are FLOAT values
        pdm.male,
        pdm.female,
        pdm.age_5_14,
        pdm.age_15_17,
        pdm.age_18_24,
        pdm.age_25_39,
        pdm.age_40_54,
        pdm.age_55_64,
        pdm.age_65_plus,
        pdm.Alexandra,
        pdm.Area_Outside_Region_Land,
        pdm.Ashburton,
        pdm.Auckland,
        pdm.Auckland_Region_Land,
        pdm.Auckland_Region_Water,
        pdm.Bay_of_Plenty_Region_Land,
        pdm.Bay_of_Plenty_Region_Water,
        pdm.Blenheim,
        pdm.Canterbury_Region_Land,
        pdm.Canterbury_Region_Water,
        pdm.Cambridge,
        pdm.Christchurch,
        pdm.Cromwell,
        pdm.Dannevirke,
        pdm.Dunedin,
        pdm.Feilding,
        pdm.Gisborne,
        pdm.Gisborne_Region_Land,
        pdm.Gore,
        pdm.Greymouth,
        pdm.Hamilton,
        pdm.Hastings,
        pdm.Hawkes_Bay_Region_Land,
        pdm.Huntly,
        pdm.Hawera,
        pdm.Invercargill,
        pdm.Kaitaia,
        pdm.Kapiti_Coast,
        pdm.Kawerau,
        pdm.Kerikeri,
        pdm.Levin,
        pdm.Manawatu_Whanganui_Region_Land,
        pdm.Marlborough_Region_Land,
        pdm.Marton,
        pdm.Masterton,
        pdm.Matamata,
        pdm.Morrinsville,
        pdm.Motueka,
        pdm.Napier,
        pdm.Nelson,
        pdm.New_Plymouth,
        pdm.Northland_Region_Land,
        pdm.Northland_Region_Water,
        pdm.Oamaru,
        pdm.Otago_Region_Land,
        pdm.Otago_Region_Water,
        pdm.Otaki,
        pdm.Palmerston_North,
        pdm.Queenstown,
        pdm.Rotorua,
        pdm.Southland_Region_Land,
        pdm.Southland_Region_Water,
        pdm.Stratford,
        pdm.Taranaki_Region_Land,
        pdm.Tasman_Region_Land,
        pdm.Taupo,
        pdm.Tauranga,
        pdm.Te_Awamutu,
        pdm.Te_Puke,
        pdm.Thames,
        pdm.Timaru,
        pdm.Tokoroa,
        pdm.Waihi,
        pdm.Waikato_Region_Land,
        pdm.Waikato_Region_Water,
        pdm.Warkworth,
        pdm.Wellington,
        pdm.Wellington_Region_Land,
        pdm.Wellington_Region_Water,
        pdm.West_Coast_Region_Land,
        pdm.Whakatane,
        pdm.Whanganui,
        pdm.Whangarei,
        pdm.Whitianga,
        pdm.Wanaka
    FROM
        sum_of_weekly_reach_by_panel as swrp
    INNER JOIN 
        temp_tables.panel_id_demographics_and_fas as pdm -- tag: table
    ON 
        swrp.panel_id = pdm.panel_id
),
-- Example of reach_with_panel_demographics data:
-- panel_id | first_week_index | total_reach | placebased_reach | roadside_reach | digital_reach | static_reach | male | female | age_5_14 | age_15_17 | age_18_24 | age_25_39 | age_40_54 | age_55_64 | age_65_plus
-- 2984     | 12               | 21637       | 21637            | 0              | 21637         | 0            | 0.51 | 0.49   | 0.12     | 0.03      | 0.08      | 0.19      | 0.18      | 0.12      | 0.19


-- STEP: Calculate reach by demographic by panel_id (total_reach * demographic percentage)
reach_by_demographic AS ( -- tag: cte
    SELECT 
        CAST(panel_id AS INT) as panel_id,
        first_week_index,
        total_reach,
        total_contacts,
        placebased_reach,
        placebased_contacts,
        roadside_reach,
        roadside_contacts,
        digital_reach,
        digital_contacts,
        static_reach,
        static_contacts,
        
        -- Share of Time (SOT) Reach
        total_reach_sot,
        placebased_reach_sot,
        roadside_reach_sot,
        digital_reach_sot,
        static_reach_sot,
        
        -- Share of Time (SOT) Impressions
        impressions,
        placebased_impressions,
        roadside_impressions,
        digital_impressions,
        static_impressions,
        
        -- These are per-panel COUNT values
        total_reach * male as total_reach_male,
        total_reach * female as total_reach_female,
        total_reach * age_5_14 as total_reach_age_5_14,
        total_reach * age_15_17 as total_reach_age_15_17,
        total_reach * age_18_24 as total_reach_age_18_24,
        total_reach * age_25_39 as total_reach_age_25_39,
        total_reach * age_40_54 as total_reach_age_40_54,
        total_reach * age_55_64 as total_reach_age_55_64,
        total_reach * age_65_plus as total_reach_age_65_plus,

        total_reach * Alexandra as total_reach_Alexandra,
        total_reach * Area_Outside_Region_Land as total_reach_Area_Outside_Region_Land,
        total_reach * Ashburton as total_reach_Ashburton,
        total_reach * Auckland as total_reach_Auckland,
        total_reach * Auckland_Region_Land as total_reach_Auckland_Region_Land,
        total_reach * Auckland_Region_Water as total_reach_Auckland_Region_Water,
        total_reach * Bay_of_Plenty_Region_Land as total_reach_Bay_of_Plenty_Region_Land,
        total_reach * Bay_of_Plenty_Region_Water as total_reach_Bay_of_Plenty_Region_Water,
        total_reach * Blenheim as total_reach_Blenheim,
        total_reach * Canterbury_Region_Land as total_reach_Canterbury_Region_Land,
        total_reach * Canterbury_Region_Water as total_reach_Canterbury_Region_Water,
        total_reach * Cambridge as total_reach_Cambridge,
        total_reach * Christchurch as total_reach_Christchurch,
        total_reach * Cromwell as total_reach_Cromwell,
        total_reach * Dannevirke as total_reach_Dannevirke,
        total_reach * Dunedin as total_reach_Dunedin,
        total_reach * Feilding as total_reach_Feilding,
        total_reach * Gisborne as total_reach_Gisborne,
        total_reach * Gisborne_Region_Land as total_reach_Gisborne_Region_Land,
        total_reach * Gore as total_reach_Gore,
        total_reach * Greymouth as total_reach_Greymouth,
        total_reach * Hamilton as total_reach_Hamilton,
        total_reach * Hastings as total_reach_Hastings,
        total_reach * Hawkes_Bay_Region_Land as total_reach_Hawkes_Bay_Region_Land,
        total_reach * Huntly as total_reach_Huntly,
        total_reach * Hawera as total_reach_Hawera,
        total_reach * Invercargill as total_reach_Invercargill,
        total_reach * Kaitaia as total_reach_Kaitaia,
        total_reach * Kapiti_Coast as total_reach_Kapiti_Coast,
        total_reach * Kawerau as total_reach_Kawerau,
        total_reach * Kerikeri as total_reach_Kerikeri,
        total_reach * Levin as total_reach_Levin,
        total_reach * Manawatu_Whanganui_Region_Land as total_reach_Manawatu_Whanganui_Region_Land,
        total_reach * Marlborough_Region_Land as total_reach_Marlborough_Region_Land,
        total_reach * Marton as total_reach_Marton,
        total_reach * Masterton as total_reach_Masterton,
        total_reach * Matamata as total_reach_Matamata,
        total_reach * Morrinsville as total_reach_Morrinsville,
        total_reach * Motueka as total_reach_Motueka,
        total_reach * Napier as total_reach_Napier,
        total_reach * Nelson as total_reach_Nelson,
        total_reach * New_Plymouth as total_reach_New_Plymouth,
        total_reach * Northland_Region_Land as total_reach_Northland_Region_Land,
        total_reach * Northland_Region_Water as total_reach_Northland_Region_Water,
        total_reach * Oamaru as total_reach_Oamaru,
        total_reach * Otago_Region_Land as total_reach_Otago_Region_Land,
        total_reach * Otago_Region_Water as total_reach_Otago_Region_Water,
        total_reach * Otaki as total_reach_Otaki,
        total_reach * Palmerston_North as total_reach_Palmerston_North,
        total_reach * Queenstown as total_reach_Queenstown,
        total_reach * Rotorua as total_reach_Rotorua,
        total_reach * Southland_Region_Land as total_reach_Southland_Region_Land,
        total_reach * Southland_Region_Water as total_reach_Southland_Region_Water,
        total_reach * Stratford as total_reach_Stratford,
        total_reach * Taranaki_Region_Land as total_reach_Taranaki_Region_Land,
        total_reach * Tasman_Region_Land as total_reach_Tasman_Region_Land,
        total_reach * Taupo as total_reach_Taupo,
        total_reach * Tauranga as total_reach_Tauranga,
        total_reach * Te_Awamutu as total_reach_Te_Awamutu,
        total_reach * Te_Puke as total_reach_Te_Puke,
        total_reach * Thames as total_reach_Thames,
        total_reach * Timaru as total_reach_Timaru,
        total_reach * Tokoroa as total_reach_Tokoroa,
        total_reach * Waihi as total_reach_Waihi,
        total_reach * Waikato_Region_Land as total_reach_Waikato_Region_Land,
        total_reach * Waikato_Region_Water as total_reach_Waikato_Region_Water,
        total_reach * Warkworth as total_reach_Warkworth,
        total_reach * Wellington as total_reach_Wellington,
        total_reach * Wellington_Region_Land as total_reach_Wellington_Region_Land,
        total_reach * Wellington_Region_Water as total_reach_Wellington_Region_Water,
        total_reach * West_Coast_Region_Land as total_reach_West_Coast_Region_Land,
        total_reach * Whakatane as total_reach_Whakatane,
        total_reach * Whanganui as total_reach_Whanganui,
        total_reach * Whangarei as total_reach_Whangarei,
        total_reach * Whitianga as total_reach_Whitianga,
        total_reach * Wanaka as total_reach_Wanaka
    FROM
        reach_with_panel_demographics
),

-- STEP: Find asset rank by dedupe curves and FUA
-- Source: models.dedupe_curves
reach_by_demographic_with_dedupe_curves AS ( -- tag: cte
    SELECT 
        rbd.*,
        pif.fua_id,
        row_number() over (partition by 1 order by rbd.total_reach desc) as reach_rank,
        row_number() over (partition by pif.fua_id order by rbd.total_reach desc) as reach_rank_within_fua
    FROM
        reach_by_demographic as rbd
    LEFT JOIN
        temp_tables.panel_id_fua as pif -- tag: table
    ON
        rbd.panel_id = pif.panel_id
),

-- STEP: Add FUA panel count to reach_by_demographic_with_dedupe_curves
reach_by_demographic_with_weeks_in_campaign AS ( -- tag: cte
    SELECT
        rbdwdc.*,
        cocw.weeks_in_campaign
    FROM    
        reach_by_demographic_with_dedupe_curves as rbdwdc
    JOIN
        count_of_weeks_in_campaign as cocw
    ON        
        cocw.id = 1
),

-- STEP: Find panel pairs to check for dedupe
-- ALL pairs are in lower number to upper number order
panel_pairs_to_check AS ( -- tag: cte
    SELECT
        CASE WHEN a.panel_id < b.panel_id THEN a.panel_id         ELSE b.panel_id END AS panel_id,
        CASE WHEN a.panel_id < b.panel_id THEN a.first_week_index ELSE b.first_week_index END AS first_week_index,
        CASE WHEN a.panel_id < b.panel_id THEN b.panel_id         ELSE a.panel_id END AS compared_panel_id,
        CASE WHEN a.panel_id < b.panel_id THEN b.first_week_index ELSE a.first_week_index END AS compared_first_week_index,
        ABS(a.first_week_index - b.first_week_index) AS weeks_between_panels,
        a.weeks_in_campaign
    FROM reach_by_demographic_with_weeks_in_campaign a
    JOIN reach_by_demographic_with_weeks_in_campaign b 
        ON b.reach_rank > a.reach_rank
),

-- STEP: Find panel pairs requiring deduplication and output a row for each panel in the pair
-- NOTE: A panel_id will likely have multiple rows in this table so subseqent steps will need to aggregate with MAX or AVG
-- Source: temp_tables.panel_id_pairwise which has both distance_meters and overlap
panel_pairs_found AS ( -- tag: cte
    SELECT 
        DISTINCT
        pptc.panel_id as panel_id,
        weeks_between_panels,
        weeks_in_campaign,
        1 AS nearby_panel,
        1 AS panel_pair,
        (1 - prp.overlap) as lower_reach_panel_dedupe_rate,
        CASE WHEN weeks_between_panels = 1 and weeks_in_campaign = 2
             THEN 1 - (0.2 * prp.overlap) 
             ELSE (1 - prp.overlap) + LEAST(weeks_between_panels * 0.02, 0.1) END 
        as lower_reach_panel_dedupe_rate2,
        prp.apply_to_panel_id
    FROM 
        panel_pairs_to_check AS pptc
    JOIN 
        temp_tables.panel_id_pairwise_lower_reach_panel_overlap_rate_four_weeks AS prp -- tag: table
    ON 
        pptc.panel_id = prp.panel_id AND 
        pptc.compared_panel_id = prp.compared_panel_id
    UNION ALL
    SELECT
        DISTINCT 
        pptc2.compared_panel_id as panel_id,
        weeks_between_panels,
        weeks_in_campaign,
        1 AS nearby_panel,
        0 AS panel_pair,
        (1 - prp2.overlap) as lower_reach_panel_dedupe_rate,
        CASE WHEN weeks_between_panels = 1 and weeks_in_campaign = 2
             THEN 1 - (0.2 * prp2.overlap) 
             ELSE (1 - prp2.overlap) + LEAST(weeks_between_panels * 0.02, 0.1) END 
        as lower_reach_panel_dedupe_rate2,
        prp2.apply_to_panel_id
    FROM 
        panel_pairs_to_check AS pptc2
    JOIN 
        temp_tables.panel_id_pairwise_lower_reach_panel_overlap_rate_four_weeks AS prp2 -- tag: table
    ON 
        pptc2.panel_id = prp2.panel_id AND 
        pptc2.compared_panel_id = prp2.compared_panel_id
),

-- STEP: Find min dedupe rate for each panel_id
overlap_panel_pairs AS ( -- tag: cte
    SELECT 
        apply_to_panel_id as panel_id,
        MIN(lower_reach_panel_dedupe_rate2) as min_lower_reach_panel_dedupe_rate
    FROM 
        panel_pairs_found
    GROUP BY
        apply_to_panel_id
),

-- STEP: Find min dedupe rate for each panel_id
overlap_panel_pairs_dropping_min AS ( -- tag: cte
    SELECT 
        ppf_opp.panel_id,
        MIN(ppf_opp.min_lower_reach_panel_dedupe_rate) as min_lower_reach_panel_dedupe_rate,
        GREATEST(MIN(ppf_opp.min_lower_reach_panel_dedupe_rate) - 
          (MIN(ppf_opp.min_lower_reach_panel_dedupe_rate) * (1 - AVG(ppf_opp.lower_reach_panel_dedupe_rate))), 0) as scaled_dedupe_rate
    FROM 
        (SELECT opp.panel_id,
                opp.min_lower_reach_panel_dedupe_rate,
                ppf.lower_reach_panel_dedupe_rate
        FROM 
           panel_pairs_found as ppf
        JOIN
            overlap_panel_pairs as opp
        ON
            ppf.apply_to_panel_id = opp.panel_id
        WHERE lower_reach_panel_dedupe_rate > min_lower_reach_panel_dedupe_rate) as ppf_opp
    GROUP BY
        ppf_opp.panel_id
),

-- STEP: Find union of min dedupe rate and scaled dedupe rate
overlap_panel_pairs_combined AS ( -- tag: cte
    SELECT 
        mp.panel_id,
        mp.min_lower_reach_panel_dedupe_rate,
        sp.scaled_dedupe_rate,
        COALESCE(sp.scaled_dedupe_rate, mp.min_lower_reach_panel_dedupe_rate) as scaled_dedupe_rate2
    FROM 
        overlap_panel_pairs as mp
    LEFT JOIN
        overlap_panel_pairs_dropping_min as sp
    ON
        mp.panel_id = sp.panel_id
),

-- STEP: Calculate keep rates
dedupe_rate_reach_by_demographic AS ( -- tag: cte
    SELECT 
        rbdwpc.*,
        GREATEST(COALESCE(opp.scaled_dedupe_rate2, 1), 0.01) as scaled_dedupe_rate2, 
        CASE WHEN rbdwpc.reach_rank_within_fua > 1 THEN 0.99 ELSE 1.0 END as same_fua_dedupe_rate,
        LEAST(GREATEST(COALESCE(opp.scaled_dedupe_rate2, 1), 0.01), 
              CASE WHEN rbdwpc.reach_rank_within_fua > 1 THEN 0.99 ELSE 1.0 END)
              as dedupe_rate
    FROM
        reach_by_demographic_with_weeks_in_campaign as rbdwpc
        
    LEFT JOIN
        overlap_panel_pairs_combined as opp
    ON
        rbdwpc.panel_id = opp.panel_id

    ORDER BY
        dedupe_rate DESC,
        rbdwpc.fua_id
),

-- STEP: Calculate total undeduplicated reach
total_undeduplicated_reach AS ( -- tag: cte
    SELECT
        SUM(total_reach) as total_reach_no_dedupe
    FROM
        dedupe_rate_reach_by_demographic
),

-- STEP: Calculate total_reach_cap
fit_undeduplicated_reach AS ( -- tag: cte
    SELECT
        rcp.id,
        rcp.reach_rate_constant,
        rcp.reach_rate_exponent,
        rcp.reach_rate_constant * POW(total_reach_no_dedupe, rcp.reach_rate_exponent) as reach_rate,
        total_reach_no_dedupe,
        rcp.reach_rate_constant * POW(total_reach_no_dedupe, rcp.reach_rate_exponent) * total_reach_no_dedupe as total_reach_cap
    FROM
        total_undeduplicated_reach
    JOIN
        temp_tables.reach_cap_params as rcp -- tag: table
    ON
        rcp.id = 1
),

-- STEP: Find population for all FUAs in campaign
fua_population AS ( -- tag: cte
    SELECT
        fua_id,
        fua_name,
        population
    FROM
        areas.dim_fua_population -- tag: table
    WHERE
        fua_id IN (SELECT DISTINCT fua_id 
                   FROM reach_by_demographic_with_weeks_in_campaign)
),

-- STEP: Calculate sum of reach by demographic
total_reach_by_demographic AS ( -- tag: cte
    SELECT  
        fua_id,
        SUM(total_reach_male * dedupe_rate) as total_reach_male,
        SUM(total_reach_female * dedupe_rate) as total_reach_female,
        SUM(total_reach_age_5_14 * dedupe_rate) as total_reach_age_5_14,
        SUM(total_reach_age_15_17 * dedupe_rate) as total_reach_age_15_17,
        SUM(total_reach_age_18_24 * dedupe_rate) as total_reach_age_18_24,
        SUM(total_reach_age_25_39 * dedupe_rate) as total_reach_age_25_39,
        SUM(total_reach_age_40_54 * dedupe_rate) as total_reach_age_40_54,
        SUM(total_reach_age_55_64 * dedupe_rate) as total_reach_age_55_64,
        SUM(total_reach_age_65_plus * dedupe_rate) as total_reach_age_65_plus,

        SUM(total_reach_Alexandra * dedupe_rate) as total_reach_Alexandra,
        SUM(total_reach_Area_Outside_Region_Land * dedupe_rate) as total_reach_Area_Outside_Region_Land,
        SUM(total_reach_Ashburton * dedupe_rate) as total_reach_Ashburton,
        SUM(total_reach_Auckland * dedupe_rate) as total_reach_Auckland,
        SUM(total_reach_Auckland_Region_Land * dedupe_rate) as total_reach_Auckland_Region_Land,
        SUM(total_reach_Auckland_Region_Water * dedupe_rate) as total_reach_Auckland_Region_Water,
        SUM(total_reach_Bay_of_Plenty_Region_Land * dedupe_rate) as total_reach_Bay_of_Plenty_Region_Land,
        SUM(total_reach_Bay_of_Plenty_Region_Water * dedupe_rate) as total_reach_Bay_of_Plenty_Region_Water,
        SUM(total_reach_Blenheim * dedupe_rate) as total_reach_Blenheim,
        SUM(total_reach_Canterbury_Region_Land * dedupe_rate) as total_reach_Canterbury_Region_Land,
        SUM(total_reach_Canterbury_Region_Water * dedupe_rate) as total_reach_Canterbury_Region_Water,
        SUM(total_reach_Cambridge * dedupe_rate) as total_reach_Cambridge,
        SUM(total_reach_Christchurch * dedupe_rate) as total_reach_Christchurch,
        SUM(total_reach_Cromwell * dedupe_rate) as total_reach_Cromwell,
        SUM(total_reach_Dannevirke * dedupe_rate) as total_reach_Dannevirke,
        SUM(total_reach_Dunedin * dedupe_rate) as total_reach_Dunedin,
        SUM(total_reach_Feilding * dedupe_rate) as total_reach_Feilding,
        SUM(total_reach_Gisborne * dedupe_rate) as total_reach_Gisborne,
        SUM(total_reach_Gisborne_Region_Land * dedupe_rate) as total_reach_Gisborne_Region_Land,
        SUM(total_reach_Gore * dedupe_rate) as total_reach_Gore,
        SUM(total_reach_Greymouth * dedupe_rate) as total_reach_Greymouth,
        SUM(total_reach_Hamilton * dedupe_rate) as total_reach_Hamilton,
        SUM(total_reach_Hastings * dedupe_rate) as total_reach_Hastings,
        SUM(total_reach_Hawkes_Bay_Region_Land * dedupe_rate) as total_reach_Hawkes_Bay_Region_Land,
        SUM(total_reach_Huntly * dedupe_rate) as total_reach_Huntly,
        SUM(total_reach_Hawera * dedupe_rate) as total_reach_Hawera,
        SUM(total_reach_Invercargill * dedupe_rate) as total_reach_Invercargill,
        SUM(total_reach_Kaitaia * dedupe_rate) as total_reach_Kaitaia,
        SUM(total_reach_Kapiti_Coast * dedupe_rate) as total_reach_Kapiti_Coast,
        SUM(total_reach_Kawerau * dedupe_rate) as total_reach_Kawerau,
        SUM(total_reach_Kerikeri * dedupe_rate) as total_reach_Kerikeri,
        SUM(total_reach_Levin * dedupe_rate) as total_reach_Levin,
        SUM(total_reach_Manawatu_Whanganui_Region_Land * dedupe_rate) as total_reach_Manawatu_Whanganui_Region_Land,
        SUM(total_reach_Marlborough_Region_Land * dedupe_rate) as total_reach_Marlborough_Region_Land,
        SUM(total_reach_Marton * dedupe_rate) as total_reach_Marton,
        SUM(total_reach_Masterton * dedupe_rate) as total_reach_Masterton,
        SUM(total_reach_Matamata * dedupe_rate) as total_reach_Matamata,
        SUM(total_reach_Morrinsville * dedupe_rate) as total_reach_Morrinsville,
        SUM(total_reach_Motueka * dedupe_rate) as total_reach_Motueka,
        SUM(total_reach_Napier * dedupe_rate) as total_reach_Napier,
        SUM(total_reach_Nelson * dedupe_rate) as total_reach_Nelson,
        SUM(total_reach_New_Plymouth * dedupe_rate) as total_reach_New_Plymouth,
        SUM(total_reach_Northland_Region_Land * dedupe_rate) as total_reach_Northland_Region_Land,
        SUM(total_reach_Northland_Region_Water * dedupe_rate) as total_reach_Northland_Region_Water,
        SUM(total_reach_Oamaru * dedupe_rate) as total_reach_Oamaru,
        SUM(total_reach_Otago_Region_Land * dedupe_rate) as total_reach_Otago_Region_Land,
        SUM(total_reach_Otago_Region_Water * dedupe_rate) as total_reach_Otago_Region_Water,
        SUM(total_reach_Otaki * dedupe_rate) as total_reach_Otaki,
        SUM(total_reach_Palmerston_North * dedupe_rate) as total_reach_Palmerston_North,
        SUM(total_reach_Queenstown * dedupe_rate) as total_reach_Queenstown,
        SUM(total_reach_Rotorua * dedupe_rate) as total_reach_Rotorua,
        SUM(total_reach_Southland_Region_Land * dedupe_rate) as total_reach_Southland_Region_Land,
        SUM(total_reach_Southland_Region_Water * dedupe_rate) as total_reach_Southland_Region_Water,
        SUM(total_reach_Stratford * dedupe_rate) as total_reach_Stratford,
        SUM(total_reach_Taranaki_Region_Land * dedupe_rate) as total_reach_Taranaki_Region_Land,
        SUM(total_reach_Tasman_Region_Land * dedupe_rate) as total_reach_Tasman_Region_Land,
        SUM(total_reach_Taupo * dedupe_rate) as total_reach_Taupo,
        SUM(total_reach_Tauranga * dedupe_rate) as total_reach_Tauranga,
        SUM(total_reach_Te_Awamutu * dedupe_rate) as total_reach_Te_Awamutu,
        SUM(total_reach_Te_Puke * dedupe_rate) as total_reach_Te_Puke,
        SUM(total_reach_Thames * dedupe_rate) as total_reach_Thames,
        SUM(total_reach_Timaru * dedupe_rate) as total_reach_Timaru,
        SUM(total_reach_Tokoroa * dedupe_rate) as total_reach_Tokoroa,
        SUM(total_reach_Waihi * dedupe_rate) as total_reach_Waihi,
        SUM(total_reach_Waikato_Region_Land * dedupe_rate) as total_reach_Waikato_Region_Land,
        SUM(total_reach_Waikato_Region_Water * dedupe_rate) as total_reach_Waikato_Region_Water,
        SUM(total_reach_Warkworth * dedupe_rate) as total_reach_Warkworth,
        SUM(total_reach_Wellington * dedupe_rate) as total_reach_Wellington,
        SUM(total_reach_Wellington_Region_Land * dedupe_rate) as total_reach_Wellington_Region_Land,
        SUM(total_reach_Wellington_Region_Water * dedupe_rate) as total_reach_Wellington_Region_Water,
        SUM(total_reach_West_Coast_Region_Land * dedupe_rate) as total_reach_West_Coast_Region_Land,
        SUM(total_reach_Whakatane * dedupe_rate) as total_reach_Whakatane,
        SUM(total_reach_Whanganui * dedupe_rate) as total_reach_Whanganui,
        SUM(total_reach_Whangarei * dedupe_rate) as total_reach_Whangarei,
        SUM(total_reach_Whitianga * dedupe_rate) as total_reach_Whitianga,
        SUM(total_reach_Wanaka * dedupe_rate) as total_reach_Wanaka,

        -- Do not deduplicate contacts
        GREATEST(ROUND(SUM(total_reach * dedupe_rate)), 1) AS t_reach, -- optimized to 15+, 4-15 year olds are extra. GREATEST 1 is to avoid divide by zero.
        ROUND(SUM(total_contacts)) AS t_contacts,
        ROUND(SUM(placebased_reach * dedupe_rate)) AS pb_reach,
        ROUND(SUM(placebased_contacts)) AS pb_contacts,
        ROUND(SUM(roadside_reach * dedupe_rate)) AS rs_reach,
        ROUND(SUM(roadside_contacts)) AS rs_contacts,
        ROUND(SUM(digital_reach * dedupe_rate)) AS d_reach,
        ROUND(SUM(digital_contacts)) AS d_contacts,
        ROUND(SUM(static_reach * dedupe_rate)) AS s_reach,
        ROUND(SUM(static_contacts)) AS s_contacts,
        ROUND(SUM(total_reach_sot * dedupe_rate)) AS t_reach_sot,
        ROUND(SUM(placebased_reach_sot * dedupe_rate)) AS pb_reach_sot,
        ROUND(SUM(roadside_reach_sot * dedupe_rate)) AS rs_reach_sot,
        ROUND(SUM(digital_reach_sot * dedupe_rate)) AS d_reach_sot,
        ROUND(SUM(static_reach_sot * dedupe_rate)) AS s_reach_sot,
        ROUND(SUM(impressions)) AS t_impressions,
        ROUND(SUM(placebased_impressions)) AS pb_impressions,
        ROUND(SUM(roadside_impressions)) AS rs_impressions,
        ROUND(SUM(digital_impressions)) AS d_impressions,
        ROUND(SUM(static_impressions)) AS s_impressions,
    FROM
        dedupe_rate_reach_by_demographic
    GROUP BY
        fua_id
),

panel_count_check AS ( -- tag: cte
    SELECT
        1 as id,
        pic.number_of_panels_in,
        poc.number_of_panels_out,
        CASE WHEN pic.number_of_panels_in = poc.number_of_panels_out THEN TRUE ELSE FALSE END as panels_in_count_matches_out_count,
        poc.missing_panel_ids
    FROM
        panels_in_count as pic 
    JOIN
        panels_out_count as poc
    ON
        poc.id = 1),
       
-- STEP: Calculate ratio of reach by demographic
reach_by_demographic_ratio AS ( -- tag: cte
    SELECT 
        fp.fua_id,
        fp.fua_name,
        fp.population,
        total_reach_male / t_reach AS ratio_male,
        -- total_reach_female / t_reach AS ratio_female,
        1.0 - (total_reach_male / t_reach) AS ratio_female,
        
        total_reach_age_5_14 / t_reach AS ratio_5_14,
        total_reach_age_15_17 / t_reach AS ratio_15_17,
        total_reach_age_18_24 / t_reach AS ratio_18_24,
        total_reach_age_25_39 / t_reach AS ratio_25_39,
        total_reach_age_40_54 / t_reach AS ratio_40_54,
        total_reach_age_55_64 / t_reach AS ratio_55_64,
        -- total_reach_age_65_plus / t_reach AS ratio_65_plus,
        1.0 - (total_reach_age_15_17 / t_reach) 
            - (total_reach_age_18_24 / t_reach) 
            - (total_reach_age_25_39 / t_reach) 
            - (total_reach_age_40_54 / t_reach) 
            - (total_reach_age_55_64 / t_reach) AS ratio_65_plus,

        total_reach_Alexandra / t_reach AS ratio_Alexandra,
        total_reach_Area_Outside_Region_Land / t_reach AS ratio_Area_Outside_Region_Land,
        total_reach_Ashburton / t_reach AS ratio_Ashburton,
        total_reach_Auckland / t_reach AS ratio_Auckland,
        total_reach_Auckland_Region_Land / t_reach AS ratio_Auckland_Region_Land,
        total_reach_Auckland_Region_Water / t_reach AS ratio_Auckland_Region_Water,
        total_reach_Bay_of_Plenty_Region_Land / t_reach AS ratio_Bay_of_Plenty_Region_Land,
        total_reach_Bay_of_Plenty_Region_Water / t_reach AS ratio_Bay_of_Plenty_Region_Water,
        total_reach_Blenheim / t_reach AS ratio_Blenheim,
        total_reach_Cambridge / t_reach AS ratio_Cambridge,
        total_reach_Canterbury_Region_Land / t_reach AS ratio_Canterbury_Region_Land,
        total_reach_Canterbury_Region_Water / t_reach AS ratio_Canterbury_Region_Water,
        total_reach_Christchurch / t_reach AS ratio_Christchurch,
        total_reach_Cromwell / t_reach AS ratio_Cromwell,
        total_reach_Dannevirke / t_reach AS ratio_Dannevirke,
        total_reach_Dunedin / t_reach AS ratio_Dunedin,
        total_reach_Feilding / t_reach AS ratio_Feilding,
        total_reach_Gisborne / t_reach AS ratio_Gisborne,
        total_reach_Gisborne_Region_Land / t_reach AS ratio_Gisborne_Region_Land,
        total_reach_Gore / t_reach AS ratio_Gore,
        total_reach_Greymouth / t_reach AS ratio_Greymouth,
        total_reach_Hamilton / t_reach AS ratio_Hamilton,
        total_reach_Hastings / t_reach AS ratio_Hastings,
        total_reach_Hawera / t_reach AS ratio_Hawera,
        total_reach_Hawkes_Bay_Region_Land / t_reach AS ratio_Hawkes_Bay_Region_Land,
        total_reach_Huntly / t_reach AS ratio_Huntly,
        total_reach_Invercargill / t_reach AS ratio_Invercargill,
        total_reach_Kaitaia / t_reach AS ratio_Kaitaia,
        total_reach_Kapiti_Coast / t_reach AS ratio_Kapiti_Coast,
        total_reach_Kawerau / t_reach AS ratio_Kawerau,
        total_reach_Kerikeri / t_reach AS ratio_Kerikeri,
        total_reach_Levin / t_reach AS ratio_Levin,
        total_reach_Manawatu_Whanganui_Region_Land / t_reach AS ratio_Manawatu_Whanganui_Region_Land,
        total_reach_Marlborough_Region_Land / t_reach AS ratio_Marlborough_Region_Land,
        total_reach_Marton / t_reach AS ratio_Marton,
        total_reach_Masterton / t_reach AS ratio_Masterton,
        total_reach_Matamata / t_reach AS ratio_Matamata,
        total_reach_Morrinsville / t_reach AS ratio_Morrinsville,
        total_reach_Motueka / t_reach AS ratio_Motueka,
        total_reach_Napier / t_reach AS ratio_Napier,
        total_reach_Nelson / t_reach AS ratio_Nelson,
        total_reach_New_Plymouth / t_reach AS ratio_New_Plymouth,
        total_reach_Northland_Region_Land / t_reach AS ratio_Northland_Region_Land,
        total_reach_Northland_Region_Water / t_reach AS ratio_Northland_Region_Water,
        total_reach_Oamaru / t_reach AS ratio_Oamaru,
        total_reach_Otago_Region_Land / t_reach AS ratio_Otago_Region_Land,
        total_reach_Otago_Region_Water / t_reach AS ratio_Otago_Region_Water,
        total_reach_Otaki / t_reach AS ratio_Otaki,
        total_reach_Palmerston_North / t_reach AS ratio_Palmerston_North,
        total_reach_Queenstown / t_reach AS ratio_Queenstown,
        total_reach_Rotorua / t_reach AS ratio_Rotorua,
        total_reach_Southland_Region_Land / t_reach AS ratio_Southland_Region_Land,
        total_reach_Southland_Region_Water / t_reach AS ratio_Southland_Region_Water,
        total_reach_Stratford / t_reach AS ratio_Stratford,
        total_reach_Taranaki_Region_Land / t_reach AS ratio_Taranaki_Region_Land,
        total_reach_Tasman_Region_Land / t_reach AS ratio_Tasman_Region_Land,
        total_reach_Taupo / t_reach AS ratio_Taupo,
        total_reach_Tauranga / t_reach AS ratio_Tauranga,
        total_reach_Te_Awamutu / t_reach AS ratio_Te_Awamutu,
        total_reach_Te_Puke / t_reach AS ratio_Te_Puke,
        total_reach_Thames / t_reach AS ratio_Thames,
        total_reach_Timaru / t_reach AS ratio_Timaru,
        total_reach_Tokoroa / t_reach AS ratio_Tokoroa,
        total_reach_Waihi / t_reach AS ratio_Waihi,
        total_reach_Waikato_Region_Land / t_reach AS ratio_Waikato_Region_Land,
        total_reach_Waikato_Region_Water / t_reach AS ratio_Waikato_Region_Water,
        total_reach_Wanaka / t_reach AS ratio_Wanaka,
        total_reach_Warkworth / t_reach AS ratio_Warkworth,
        total_reach_Wellington / t_reach AS ratio_Wellington,
        total_reach_Wellington_Region_Land / t_reach AS ratio_Wellington_Region_Land,
        total_reach_Wellington_Region_Water / t_reach AS ratio_Wellington_Region_Water,
        total_reach_West_Coast_Region_Land / t_reach AS ratio_West_Coast_Region_Land,
        total_reach_Whakatane / t_reach AS ratio_Whakatane,
        total_reach_Whanganui / t_reach AS ratio_Whanganui,
        total_reach_Whangarei / t_reach AS ratio_Whangarei,
        total_reach_Whitianga / t_reach AS ratio_Whitianga,

        t_reach,    -- total reach
        t_contacts, -- total contacts
        pb_reach,    -- placebased reach
        pb_contacts, -- placebased contacts
        rs_reach,    -- roadside reach
        rs_contacts, -- roadside contacts
        d_reach,    -- digital reach
        d_contacts, -- digital contacts
        s_reach,   -- static reach
        s_contacts, -- static contacts
        t_reach_sot,    -- total reach sot
        pb_reach_sot,    -- placebased reach sot
        rs_reach_sot,    -- roadside reach sot
        d_reach_sot,    -- digital reach sot
        s_reach_sot,   -- static reach sot
        t_impressions, -- total impressions
        pb_impressions, -- placebased impressions
        rs_impressions, -- roadside impressions
        d_impressions, -- digital impressions
        s_impressions, -- static impressions
        t_reach / fp.population as reach_to_population_ratio,
        t_reach_sot / fp.population as reach_sot_to_population_ratio
    FROM
        total_reach_by_demographic as trbd
    INNER JOIN
        fua_population as fp
    ON
        trbd.fua_id = fp.fua_id),

-- STEP: Calculate population capped reach
capped_reach_by_demographic_ratio AS ( -- tag: cte
    SELECT
        fua_id,
        fua_name,
        population,
        ratio_male,
        ratio_female,
        
        ratio_5_14,
        ratio_15_17,
        ratio_18_24,
        ratio_25_39,
        ratio_40_54,
        ratio_55_64,
        ratio_65_plus,

        ratio_Alexandra,
        ratio_Area_Outside_Region_Land,
        ratio_Ashburton,
        ratio_Auckland,
        ratio_Auckland_Region_Land,
        ratio_Auckland_Region_Water,
        ratio_Bay_of_Plenty_Region_Land,
        ratio_Bay_of_Plenty_Region_Water,
        ratio_Blenheim,
        ratio_Cambridge,
        ratio_Canterbury_Region_Land,
        ratio_Canterbury_Region_Water,
        ratio_Christchurch,
        ratio_Cromwell,
        ratio_Dannevirke,
        ratio_Dunedin,
        ratio_Feilding,
        ratio_Gisborne,
        ratio_Gisborne_Region_Land,
        ratio_Gore,
        ratio_Greymouth,
        ratio_Hamilton,
        ratio_Hastings,
        ratio_Hawera,
        ratio_Hawkes_Bay_Region_Land,
        ratio_Huntly,
        ratio_Invercargill,
        ratio_Kaitaia,
        ratio_Kapiti_Coast,
        ratio_Kawerau,
        ratio_Kerikeri,
        ratio_Levin,
        ratio_Manawatu_Whanganui_Region_Land,
        ratio_Marlborough_Region_Land,
        ratio_Marton,
        ratio_Masterton,
        ratio_Matamata,
        ratio_Morrinsville,
        ratio_Motueka,
        ratio_Napier,
        ratio_Nelson,
        ratio_New_Plymouth,
        ratio_Northland_Region_Land,
        ratio_Northland_Region_Water,
        ratio_Oamaru,
        ratio_Otago_Region_Land,
        ratio_Otago_Region_Water,
        ratio_Otaki,
        ratio_Palmerston_North,
        ratio_Queenstown,
        ratio_Rotorua,
        ratio_Southland_Region_Land,
        ratio_Southland_Region_Water,
        ratio_Stratford,
        ratio_Taranaki_Region_Land,
        ratio_Tasman_Region_Land,
        ratio_Taupo,
        ratio_Tauranga,
        ratio_Te_Awamutu,
        ratio_Te_Puke,
        ratio_Thames,
        ratio_Timaru,
        ratio_Tokoroa,
        ratio_Waihi,
        ratio_Waikato_Region_Land,
        ratio_Waikato_Region_Water,
        ratio_Wanaka,
        ratio_Warkworth,
        ratio_Wellington,
        ratio_Wellington_Region_Land,
        ratio_Wellington_Region_Water,
        ratio_West_Coast_Region_Land,
        ratio_Whakatane,
        ratio_Whanganui,
        ratio_Whangarei,
        ratio_Whitianga,

        t_reach * CASE WHEN reach_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_to_population_ratio END as t_reach,    -- total reach
        t_contacts, -- total contacts
        pb_reach * CASE WHEN reach_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_to_population_ratio END as pb_reach,    -- placebased reach
        pb_contacts, -- placebased contacts
        rs_reach * CASE WHEN reach_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_to_population_ratio END as rs_reach,    -- roadside reach
        rs_contacts, -- roadside contacts
        d_reach * CASE WHEN reach_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_to_population_ratio END as d_reach,    -- digital reach
        d_contacts, -- digital contacts
        s_reach * CASE WHEN reach_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_to_population_ratio END as s_reach,   -- static reach
        s_contacts, -- static contacts

        t_reach_sot * CASE WHEN reach_sot_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_sot_to_population_ratio END as t_reach_sot,    -- total reach sot
        t_impressions, -- total impressions 
        pb_reach_sot * CASE WHEN reach_sot_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_sot_to_population_ratio END as pb_reach_sot,    -- placebased reach sot
        pb_impressions, -- placebased impressions
        rs_reach_sot * CASE WHEN reach_sot_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_sot_to_population_ratio END as rs_reach_sot,    -- roadside reach sot
        rs_impressions, -- roadside impressions
        d_reach_sot * CASE WHEN reach_sot_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_sot_to_population_ratio END as d_reach_sot,    -- digital reach sot
        d_impressions, -- digital impressions
        s_reach_sot * CASE WHEN reach_sot_to_population_ratio <= 1.0 THEN 1.0 ELSE 1/reach_sot_to_population_ratio END as s_reach_sot,   -- static reach sot
        s_impressions, -- static impressions        
    FROM
        reach_by_demographic_ratio
),        

-- STEP: Calculate total undeduplicated reach
total_deduplicated_reach AS ( -- tag: cte
    SELECT
        SUM(t_reach) as total_reach_deduped,
        fur.id,
        fur.reach_rate,
        fur.total_reach_cap
    FROM
        capped_reach_by_demographic_ratio
    JOIN
        fit_undeduplicated_reach as fur
    ON
        TRUE
    GROUP BY 
        fur.id,
        fur.reach_rate,
        fur.total_reach_cap        
),

-- STEP: Calculate total_reach_capped reach
capped_by_reach_rate AS ( -- tag: cte
    SELECT
        fua_id,
        fua_name,
        population,
        ratio_male,
        ratio_female,
        
        ratio_5_14,
        ratio_15_17,
        ratio_18_24,
        ratio_25_39,
        ratio_40_54,
        ratio_55_64,
        ratio_65_plus,

        ratio_Alexandra,
        ratio_Area_Outside_Region_Land,
        ratio_Ashburton,
        ratio_Auckland,
        ratio_Auckland_Region_Land,
        ratio_Auckland_Region_Water,
        ratio_Bay_of_Plenty_Region_Land,
        ratio_Bay_of_Plenty_Region_Water,
        ratio_Blenheim,
        ratio_Cambridge,
        ratio_Canterbury_Region_Land,
        ratio_Canterbury_Region_Water,
        ratio_Christchurch,
        ratio_Cromwell,
        ratio_Dannevirke,
        ratio_Dunedin,
        ratio_Feilding,
        ratio_Gisborne,
        ratio_Gisborne_Region_Land,
        ratio_Gore,
        ratio_Greymouth,
        ratio_Hamilton,
        ratio_Hastings,
        ratio_Hawera,
        ratio_Hawkes_Bay_Region_Land,
        ratio_Huntly,
        ratio_Invercargill,
        ratio_Kaitaia,
        ratio_Kapiti_Coast,
        ratio_Kawerau,
        ratio_Kerikeri,
        ratio_Levin,
        ratio_Manawatu_Whanganui_Region_Land,
        ratio_Marlborough_Region_Land,
        ratio_Marton,
        ratio_Masterton,
        ratio_Matamata,
        ratio_Morrinsville,
        ratio_Motueka,
        ratio_Napier,
        ratio_Nelson,
        ratio_New_Plymouth,
        ratio_Northland_Region_Land,
        ratio_Northland_Region_Water,
        ratio_Oamaru,
        ratio_Otago_Region_Land,
        ratio_Otago_Region_Water,
        ratio_Otaki,
        ratio_Palmerston_North,
        ratio_Queenstown,
        ratio_Rotorua,
        ratio_Southland_Region_Land,
        ratio_Southland_Region_Water,
        ratio_Stratford,
        ratio_Taranaki_Region_Land,
        ratio_Tasman_Region_Land,
        ratio_Taupo,
        ratio_Tauranga,
        ratio_Te_Awamutu,
        ratio_Te_Puke,
        ratio_Thames,
        ratio_Timaru,
        ratio_Tokoroa,
        ratio_Waihi,
        ratio_Waikato_Region_Land,
        ratio_Waikato_Region_Water,
        ratio_Wanaka,
        ratio_Warkworth,
        ratio_Wellington,
        ratio_Wellington_Region_Land,
        ratio_Wellington_Region_Water,
        ratio_West_Coast_Region_Land,
        ratio_Whakatane,
        ratio_Whanganui,
        ratio_Whangarei,
        ratio_Whitianga,

        t_reach * CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as t_reach,    -- total reach
        t_contacts, -- total contacts
        pb_reach * CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as pb_reach,    -- placebased reach
        pb_contacts, -- placebased contacts
        rs_reach * CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as rs_reach,    -- roadside reach
        rs_contacts, -- roadside contacts
        d_reach * CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as d_reach,    -- digital reach
        d_contacts, -- digital contacts
        s_reach * CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as s_reach,   -- static reach
        s_contacts, -- static contacts
        -- Share of Time (SOT) Reach and Impressions
        t_reach_sot* CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as t_reach_sot,    -- total reach sot
        t_impressions, -- total impressions
        pb_reach_sot* CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as pb_reach_sot,    -- placebased reach sot
        pb_impressions, -- placebased impressions
        rs_reach_sot* CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as rs_reach_sot,    -- roadside reach sot
        rs_impressions, -- roadside impressions
        d_reach_sot* CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as d_reach_sot,    -- digital reach sot
        d_impressions, -- digital impressions
        s_reach_sot* CASE WHEN total_reach_deduped > total_reach_cap THEN total_reach_cap / total_reach_deduped ELSE 1.0 END as s_reach_sot,   -- static reach sot
        s_impressions -- static impressions        
    FROM
        capped_reach_by_demographic_ratio
    JOIN
        total_deduplicated_reach as tdr
    ON
        tdr.id = 1
),


-- STEP: Combine reach by demographic ratio with panel count check
combined_rows AS ( -- tag: cte
    SELECT
        ppc.number_of_panels_in,
        ppc.number_of_panels_out,
        ppc.panels_in_count_matches_out_count,
        ppc.missing_panel_ids,
        cbrr.*
    FROM
        panel_count_check as ppc
    LEFT JOIN
        capped_by_reach_rate as cbrr
    ON
        ppc.id = 1)

-- STEP: Final select
SELECT * FROM combined_rows
"""

SQL_BULK_PANEL_HEAD = """
-- SQL_BULK_PANEL_HEAD
-- STEP: Get all panel_ids from dim_panel_vc_inc
--       Converting selected from_date and to_date to weeks within the interval
WITH campaign_panels_with_dates_raw AS ( -- tag: cte
    SELECT DISTINCT -- DISTINCT here prevents double booking of a panel on the exact same date
        panel_id, 
        local_date,
        days
    FROM (
        SELECT
            panel_id, 
            -- d AS local_date,
            d['unnest'] AS local_date,
            -- LEAST(date_diff(DATE(to_date), DATE(from_date), DAY) + 1, 7) AS days
            -- Modified for DuckDB
            LEAST(date_diff('day', local_date, cast('{to_date}' as date)) + 1, 7) AS days
        FROM
            (SELECT * FROM 
               mortalportal.dim_panel_vc_inc -- tag: table
             WHERE latest = True),
            -- UNNEST(GENERATE_DATE_ARRAY(DATE('{from_date}'), DATE('{to_date}'), INTERVAL 1 WEEK)) d
            UNNEST(generate_series(strptime('{from_date}', '%Y-%m-%d'), strptime('{to_date}', '%Y-%m-%d'), INTERVAL 1 WEEK)) as d
        {panel_id_condition}
    )
    ORDER BY panel_id, local_date
), 

campaign_panels_with_build_days AS ( -- tag: cte
    SELECT
        cpr.*,
        bd.build_days
    FROM
        campaign_panels_with_dates_raw as cpr
    JOIN
        models.build_days as bd -- tag: table
    ON
        cpr.days = bd.days
),

campaign_panels_with_day_of_week AS ( -- tag: cte
    SELECT 
        panel_id, 
        cp.local_date,
        days,
        build_days,
        week_index, -- has integer values from 0 for week of 2024-01-01 on through 313 for week of 2029-12-31
        -- EXTRACT(DAYOFWEEK FROM cp.local_date) as day_of_week_number
        -- Modified for DuckDB
        CASE WHEN EXTRACT(DAYOFWEEK FROM cp.local_date) = 0 THEN 7 ELSE EXTRACT(DAYOFWEEK FROM cp.local_date) END as day_of_week_number
    FROM 
        campaign_panels_with_build_days as cp
    JOIN
        temp_tables.local_date_in_week as ldiw -- tag: table
    ON
        -- cp.local_date = DATE(ldiw.local_date)
        cp.local_date = CAST(ldiw.local_date as date)
    ORDER BY 
        panel_id, 
        cp.local_date
),

panel_weeks AS ( -- tag: cte
    SELECT DISTINCT
        panel_id,
        week_index
    FROM campaign_panels_with_day_of_week
),

count_of_weeks_in_campaign AS ( -- tag: cte
    SELECT
    1 as id,
    count(DISTINCT week_index) as weeks_in_campaign
    FROM panel_weeks
),

panel_weeks_with_week_number AS ( -- tag: cte
    SELECT
        panel_id,
        week_index,
        ROW_NUMBER() OVER (PARTITION BY panel_id ORDER BY week_index) as week
    FROM panel_weeks
),

panel_first_week_index AS ( -- tag: cte
    SELECT
        panel_id,
        MIN(week_index) as first_week_index
    FROM panel_weeks_with_week_number
    GROUP BY panel_id
),

campaign_panels_with_dates AS ( -- tag: cte
    SELECT
        cpdw.*,
        pfwi.first_week_index,
        pw.week
    FROM
        campaign_panels_with_day_of_week as cpdw
    JOIN
        panel_first_week_index as pfwi
    ON
        cpdw.panel_id = pfwi.panel_id
    JOIN
        panel_weeks_with_week_number as pw
    ON
        cpdw.panel_id = pw.panel_id AND
        cpdw.week_index = pw.week_index
),
-- Example of campaign_panels_with_dates data:
-- panel_id	local_date	days	build_days week_index	day_of_week_number	first_week_index	week
-- 6222     2024-03-29   1          2.8       12               5                12                1

-- STEP: Count penels in. Should match panels with reach out later.
panels_in_count AS ( -- tag: cte
    SELECT
        1 as id,
        COUNT(DISTINCT panel_id) as number_of_panels_in
    FROM
        campaign_panels_with_dates),
-- TO BE FOLLOWED BY SQL_REACH_BODY
"""

SQL_BULK_PANEL_FINAL = """
-- SQL_BULK_PANEL_FINAL
-- Pre-requisite: SQL_REACH_BODY
results AS (
    SELECT 
        panel_id,  
        total_reach AS reach, 
        total_contacts AS contacts,
        -- Share of Time (SOT) Reach
        total_reach_sot AS reach_sot,
        placebased_reach_sot AS placebased_reach_sot,
        roadside_reach_sot AS roadside_reach_sot,
        digital_reach_sot AS digital_reach_sot,
        static_reach_sot AS static_reach_sot,
        -- Share of Time (SOT) Impressions
        impressions AS impressions,
        placebased_impressions AS placebased_impressions,
        roadside_impressions AS roadside_impressions,
        digital_impressions AS digital_impressions,
        static_impressions AS static_impressions
    FROM 
        sum_of_weekly_reach_by_panel
)
SELECT 
    r.panel_id, 
    -- dp.version,
    dp.member_panel_id,
    dp.member_name,
    dp.latitude,
    dp.longitude,
    dp.orientation,
    dp.panel_height,
    dp.panel_width,
    dp.product,
    dp.digital_static,
    -- dp.coef,
    -- (SELECT MAX(lane_count) FROM UNNEST(dp.lane_counts) AS lane_count) AS max_lanes,
    -- 0 as max_lanes,
    -- (SELECT SUM(lane_count) FROM UNNEST(dp.lane_counts) AS lane_count) AS sum_lanes,
    -- 0 as sum_lanes,
    -- CASE WHEN ASCII((SELECT MAX(hway_num) FROM UNNEST(dp.hway_nums) AS hway_num)) = 0 THEN FALSE ELSE TRUE END AS is_hway,
    -- FALSE as is_hway,
    ROUND(r.reach) AS reach, 
    ROUND(r.contacts) AS contacts,
    -- Share of Time (SOT) Reach
    ROUND(r.reach_sot) AS reach_sot,
    ROUND(r.placebased_reach_sot) AS placebased_reach_sot,
    ROUND(r.roadside_reach_sot) AS roadside_reach_sot,
    ROUND(r.digital_reach_sot) AS digital_reach_sot,
    ROUND(r.static_reach_sot) AS static_reach_sot,
    -- Share of Time (SOT) Impressions
    ROUND(r.impressions) AS impressions,
    ROUND(r.placebased_impressions) AS placebased_impressions,
    ROUND(r.roadside_impressions) AS roadside_impressions,
    ROUND(r.digital_impressions) AS digital_impressions,
    ROUND(r.static_impressions) AS static_impressions
FROM 
    results as r
LEFT JOIN 
    mortalportal.dim_panel_vc_inc as dp -- tag: table
ON 
    r.panel_id = dp.panel_id
WHERE
    dp.latest = True
"""
