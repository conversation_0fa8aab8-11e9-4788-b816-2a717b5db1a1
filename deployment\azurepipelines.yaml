trigger:
- master

pool:
  vmImage: 'ubuntu-latest'

variables:
  containerRegistry: 'geoapisotazure1cf4e7.azurecr.io'
  imageName: 'geo-api-sot-azure'
  tag: '$(Build.BuildId)'

stages:
- stage: Build
  jobs:
  - job: BuildAndPush
    steps:
    - task: AzureCLI@2
      displayName: 'Download database file'
      inputs:
        azureSubscription: 'your-service-connection'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          az storage blob download \
            --account-name yourstorageaccount \
            --container-name database-files \
            --name bigquery_clone.db \
            --file ./bigquery_clone.db \
            --auth-mode login
    
    - task: Docker@2
      displayName: 'Build and push image'
      inputs:
        containerRegistry: 'your-acr-connection'
        repository: $(imageName)
        command: 'buildAndPush'
        Dockerfile: '**/Dockerfile'
        tags: |
          $(tag)
          latest
