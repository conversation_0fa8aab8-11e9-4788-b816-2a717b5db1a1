# pylint: skip-file
# flake8: noqa

import os
from flask import Flask, jsonify, request
from geo_functions import GeoFunctions
from pydantic import ValidationError
from models import CalibreCampaign, CalibreAgeGender, ModelPanel
import json

app = Flask(__name__)
app.json.sort_keys = False

geo_funcs = GeoFunctions()

@app.route('/health', methods=["GET"])
def health():
    """
    API health check
    """
    return jsonify({"status": "geo-api-sot-azure 202507011440 is healthy!"})


@app.route('/campaigns', methods=["POST"])
def process_calibre_campaign():
    payload = request.json
    params = request.args.to_dict()

    try:
        payload = CalibreCampaign.model_validate(payload)
        params = CalibreAgeGender.model_validate(params)

        response = geo_funcs.process_calibre(payload=payload, params=params)
        return jsonify(json.loads(response.model_dump_json()))
    except ValidationError as e:
        return jsonify({'validation_error': str(e)})
    except Exception as e:
        return jsonify({'error': str(e)})
    
    
@app.route('/panels', methods=["POST"])
def process_panels():
    payload = request.json
    params = request.args.to_dict()
    try:
        payload = ModelPanel.model_validate(payload)
        params = CalibreAgeGender.model_validate(params)

        response = geo_funcs.process_panel(payload=payload, params=params)
        return jsonify(response)
    except ValidationError as e:
        return jsonify({'validation_error': str(e)})
    except Exception as e:
        return jsonify({'error': str(e)})
    

if __name__ == '__main__':
    server_port = os.environ.get('PORT', '8080')
    app.run(debug=False, port=server_port, host='0.0.0.0')
