# pylint: skip-file
# flake8: noqa
# geo-api-local geo_functions.py
# 20250303 1200 Additional coding to handle reach = 0
# 20250219 0938 removed 5-14 from default target_ages_dict
# 20250214 1621 renamed audience to contacts, avg_freq to frequency and removed audiencePotential
# 20250212 1038 Changed to FUA from TA
# 20250204 1627 Added check for reach of 0 near response.targetAudienceSummary.reach = reach

# import pandas_gbq
import pandas as pd
import numpy as np
import duckdb
from constants import (
    SQL_CALIBRE_HEAD,
    SQL_REACH_BODY,
    SQL_CALIBRE_FINAL,
    SQL_BULK_PANEL_HEAD,
    SQL_BULK_PANEL_FINAL
)
from models import CalibreResponse, CalibreCampaign, CalibreAgeGender, CalibreDemographicAudience, CalibreRegionScore, ModelPanel
from random import choices
from string import ascii_uppercase, digits
import datetime, pytz, os
# from gcsfs import GCSFileSystem

DEBUG = True
DEBUG = False
print(f"DEBUG: {DEBUG}")

VERBOSE = True
VERBOSE = False
print(f"VERBOSE: {VERBOSE}")

# GCSFileSystem()

class GeoFunctions:
    def __init__(self) -> None:
        self.bigquery_client = None

    def consolidate_date_ranges(self, df):
        """
        Consolidates contiguous date ranges in a pandas DataFrame within panel_id groups.

        Args:
            df: pandas DataFrame with 'from_date', 'to_date', and 'panel_id' columns.
                from_date and to_date should be datetime objects.

        Returns:
            pandas DataFrame with consolidated date ranges grouped by panel_id.
        """

        if df.empty:
            return df
        
        # Convert date strings to datetime objects
        df['from_date'] = pd.to_datetime(df['from_date'])
        df['to_date'] = pd.to_datetime(df['to_date'])

        # Sort by panel_id and from_date
        df = df.sort_values(by=['panel_id', 'from_date']).reset_index(drop=True)
        consolidated = []
        
        # Group by panel_id and consolidate ranges within each group
        for panel_id, group in df.groupby('panel_id'):
            if len(group) == 0:
                continue
                
            start = group['from_date'].iloc[0]
            end = group['to_date'].iloc[0]

            for i in range(1, len(group)):
                if group['from_date'].iloc[i] <= end + pd.Timedelta(days=1):
                    end = max(end, group['to_date'].iloc[i])
                else:
                    consolidated.append({
                        'panel_id': panel_id,
                        'from_date': start,
                        'to_date': end
                    })
                    start = group['from_date'].iloc[i]
                    end = group['to_date'].iloc[i]

            consolidated.append({
                'panel_id': panel_id,
                'from_date': start,
                'to_date': end
            })

        df1 = pd.DataFrame(consolidated)
        # Convert datetime back to yyyy-mm-dd string format
        df1['from_date'] = df1['from_date'].dt.strftime('%Y-%m-%d')
        df1['to_date'] = df1['to_date'].dt.strftime('%Y-%m-%d')

        return df1
    
    def get_params(self, params:CalibreAgeGender):
        genders = params.genders
        ages = params.ageRanges
        
        genders_dict = {
            'male': 'ratio_male', 
            'female': 'ratio_female'
        }

        ages_dict = {
            '5-14': 'ratio_5_14', 
            '15-17': 'ratio_15_17', 
            '18-24': 'ratio_18_24', 
            '25-39': 'ratio_25_39', 
            '40-54': 'ratio_40_54', 
            '55-64': 'ratio_55_64',
            '65': 'ratio_65_plus' 
        }

        origin_fua_dict = {
            'Alexandra':'ratio_Alexandra',
            'Ashburton':'ratio_Ashburton',
            'Auckland':'ratio_Auckland',
            'Blenheim':'ratio_Blenheim',
            'Cambridge':'ratio_Cambridge',
            'Christchurch':'ratio_Christchurch',
            'Cromwell':'ratio_Cromwell',
            'Dannevirke':'ratio_Dannevirke',
            'Dunedin':'ratio_Dunedin',
            'Feilding':'ratio_Feilding',
            'Gisborne':'ratio_Gisborne',
            'Gore':'ratio_Gore',
            'Greymouth':'ratio_Greymouth',
            'Hamilton':'ratio_Hamilton',
            'Hastings':'ratio_Hastings',
            'Huntly':'ratio_Huntly',
            'Hawera':'ratio_Hawera',
            'Invercargill':'ratio_Invercargill',
            'Kaitaia':'ratio_Kaitaia',
            'Kapiti_Coast':'ratio_Kapiti_Coast',
            'Kawerau':'ratio_Kawerau',
            'Kerikeri':'ratio_Kerikeri',
            'Levin':'ratio_Levin',
            'Marton':'ratio_Marton',
            'Masterton':'ratio_Masterton',
            'Matamata':'ratio_Matamata',
            'Morrinsville':'ratio_Morrinsville',
            'Motueka':'ratio_Motueka',
            'Napier':'ratio_Napier',
            'Nelson':'ratio_Nelson',
            'New_Plymouth':'ratio_New_Plymouth',
            'Oamaru':'ratio_Oamaru',
            'Otaki':'ratio_Otaki',
            'Palmerston_North':'ratio_Palmerston_North',
            'Queenstown':'ratio_Queenstown',
            'Rotorua':'ratio_Rotorua',
            'Stratford':'ratio_Stratford',
            'Taupo':'ratio_Taupo',
            'Tauranga':'ratio_Tauranga',
            'Te_Awamutu':'ratio_Te_Awamutu',
            'Te_Puke':'ratio_Te_Puke',
            'Thames':'ratio_Thames',
            'Timaru':'ratio_Timaru',
            'Tokoroa':'ratio_Tokoroa',
            'Waihi':'ratio_Waihi',
            'Warkworth':'ratio_Warkworth',
            'Wellington':'ratio_Wellington',
            'Whakatane':'ratio_Whakatane',
            'Whanganui':'ratio_Whanganui',
            'Whangarei':'ratio_Whangarei',
            'Whitianga':'ratio_Whitianga',
            'Wanaka':'ratio_Wanaka',
            'Land_area':['ratio_Area_Outside_Region_Land', 
                        'ratio_Auckland_Region_Land',
                        'ratio_Bay_of_Plenty_Region_Land',
                        'ratio_Canterbury_Region_Land',
                        'ratio_Northland_Region_Land',
                        'ratio_Otago_Region_Land',
                        'ratio_Waikato_Region_Land',
                        'ratio_Wellington_Region_Land',
                        'ratio_Gisborne_Region_Land',
                        'ratio_Hawkes_Bay_Region_Land',
                        'ratio_Manawatu_Whanganui_Region_Land',
                        'ratio_Marlborough_Region_Land',
                        'ratio_Southland_Region_Land',
                        'ratio_Taranaki_Region_Land',
                        'ratio_Tasman_Region_Land',
                        'ratio_West_Coast_Region_Land'],
            'Water_area':['ratio_Auckland_Region_Water',
                        'ratio_Bay_of_Plenty_Region_Water',
                        'ratio_Canterbury_Region_Water',
                        'ratio_Northland_Region_Water',
                        'ratio_Otago_Region_Water',
                        'ratio_Southland_Region_Water',
                        'ratio_Waikato_Region_Water',
                        'ratio_Wellington_Region_Water']
        }

        origin_region_dict = {
            'Auckland Region': [
                'ratio_Auckland',
                'ratio_Warkworth',
                'ratio_Auckland_Region_Land', 
                'ratio_Auckland_Region_Water',
                'ratio_Area_Outside_Region_Land'
            ],
            'Bay of Plenty Region': [
                'ratio_Kawerau',
                'ratio_Rotorua', 
                'ratio_Tauranga',
                'ratio_Te_Puke',
                'ratio_Whakatane',
                'ratio_Bay_of_Plenty_Region_Land', 
                'ratio_Bay_of_Plenty_Region_Water'
            ],
            'Canterbury Region': [
                'ratio_Ashburton',
                'ratio_Christchurch',
                'ratio_Timaru',
                'ratio_Canterbury_Region_Land', 
                'ratio_Canterbury_Region_Water'
            ],
            'Gisborne Region': [
                'ratio_Gisborne',
                'ratio_Gisborne_Region_Land'
            ],
            "Hawke's Bay Region": [
                'ratio_Hastings',
                'ratio_Napier',
                'ratio_Hawkes_Bay_Region_Land'
            ],
            'Manawatu-Whanganui Region': [
                'ratio_Dannevirke',
                'ratio_Feilding',
                'ratio_Levin', 
                'ratio_Marton',
                'ratio_Palmerston_North',
                'ratio_Whanganui',
                'ratio_Manawatu_Whanganui_Region_Land'
            ],
            'Marlborough Region': [
                'ratio_Blenheim',
                'ratio_Marlborough_Region_Land'
            ],
            'Nelson Region': [
                'ratio_Nelson'
            ],
            'Northland Region': [
                'ratio_Kaitaia',
                'ratio_Kerikeri',
                'ratio_Whangarei',
                'ratio_Northland_Region_Land', 
                'ratio_Northland_Region_Water'
            ],
            'Otago Region': [
                'ratio_Alexandra',
                'ratio_Cromwell',
                'ratio_Dunedin',
                'ratio_Oamaru', 
                'ratio_Queenstown',
                'ratio_Wanaka',
                'ratio_Otago_Region_Land', 
                'ratio_Otago_Region_Water'
            ],
            'Southland Region': [
                'ratio_Gore',
                'ratio_Invercargill',
                'ratio_Southland_Region_Land',
                'ratio_Southland_Region_Water'
            ],
            'Taranaki Region': [
                'ratio_Hawera',
                'ratio_New_Plymouth',
                'ratio_Stratford',
                'ratio_Taranaki_Region_Land'
            ],
            'Tasman Region': [
                'ratio_Motueka',
                'ratio_Tasman_Region_Land'
            ],
            'Waikato Region': [
                'ratio_Cambridge',
                'ratio_Hamilton',
                'ratio_Huntly',
                'ratio_Matamata',
                'ratio_Morrinsville',
                'ratio_Taupo',
                'ratio_Te_Awamutu',
                'ratio_Thames',
                'ratio_Tokoroa',
                'ratio_Waihi',
                'ratio_Whitianga',
                'ratio_Waikato_Region_Land', 
                'ratio_Waikato_Region_Water'
            ],
            'Wellington Region': [
                'ratio_Kapiti_Coast',
                'ratio_Masterton',
                'ratio_Otaki',
                'ratio_Wellington',
                'ratio_Wellington_Region_Land', 
                'ratio_Wellington_Region_Water'
            ],
            'West Coast Region': [
                'ratio_Greymouth',
                'ratio_West_Coast_Region_Land'
            ]
        }

        target_genders_dict = {}
        target_ages_dict = {}
        if not genders:
            target_genders_dict = genders_dict
        else:
            genders = params.genders.split(',')
            for gender in genders:
                target_genders_dict[gender] = 'ratio_{}'.format(gender)
        
        if not ages:
            target_ages_dict = {k:v for k,v in ages_dict.items() if k != '5-14'}
        else:
            ages = ages.split(',')
            for age in ages:
                if '65' in age:
                    target_ages_dict['65'] = 'ratio_65_plus'
                else:
                    target_ages_dict[age] = 'ratio_{}'.format('_'.join(age.split('-')))

        return target_genders_dict, genders_dict, target_ages_dict, ages_dict, origin_fua_dict, origin_region_dict
    
    def create_table_name(self, n: int = 6, prefix: str = 'panels'):
        population = ascii_uppercase + digits
        return '_'.join([
            prefix, 
            datetime.datetime.now().strftime('%Y%m%d%H%M%S'), 
            str.join('', choices(population, k=n))
        ])
    
    def create_temp_table(self, records: str, ttl: int = 3):
        table_name = self.create_table_name()
        return table_name

    def int_nan(self, value: float) -> int:
        if isinstance(value, float) and np.isnan(value):
            return 0
        else:
            try:
                return int(value)
            except:
                print(f"Error converting {value} to int")
                return 0

    def process_calibre(self, payload: CalibreCampaign, params: CalibreAgeGender) -> CalibreResponse:
        target_genders_dict, genders_dict, target_ages_dict, ages_dict, origin_fua_dict, origin_region_dict = self.get_params(params=params)

        pdist = params.pdist
        pratio = np.min([params.pratio, 1])
        poratio = np.min([params.poratio, 1])
        cmax = params.cmax
        cdecay = params.cdecay
        pbcoef = params.pbcoef
        
        pa = 1 - pratio
        pb = np.log(1000 * pa) / pdist
        pa_pb = np.max([1 - pbcoef * pratio, 0.001])
        pb_pb = np.log(1000 * pa_pb) / pdist
        pao = 1 - poratio
        pbo = np.log(1000 * pao) / pdist
        ca = cdecay
        cb = 1 / cmax * np.log((1 + ca - 1 / cmax) / ca)
        cc = cmax
        
        
        response = CalibreResponse()
        response.campaignId = payload.name
        flights = payload.flights

        # collect rows of temp table
        dfs = {}
        
        for index, flight in enumerate(flights):
            panel_ids = flight.clientPanelIds
            
            from_date = flight.startDate
            to_date = flight.endDate

            # For each panel ID in the flight, add a row to the temp table
            panel_ids_added = []
            start_dates = []
            end_dates = []
            for panel_id in panel_ids:
                panel_ids_added.append(panel_id)
                start_dates.append(from_date)
                end_dates.append(to_date)
            dfs[index] = pd.DataFrame({
                'panel_id': panel_ids_added,
                'from_date': start_dates,
                'to_date': end_dates
            })
            
        df_temp = pd.concat(dfs.values())

        df_temp = self.consolidate_date_ranges(df_temp)
        

        # Example output:
        # SELECT 2984 AS panel_id, '2024-07-01T00:00:00+00:00' AS from_date, '2024-07-07T00:00:00+00:00' AS to_date
        # UNION ALL
        # SELECT 2984, '2024-07-08T00:00:00+00:00', '2024-07-14T00:00:00+00:00'

        # Create temporary table with panel data
        # table_name = self.create_temp_table(records=sql_parts_str, ttl=params.ttl)
        table_name = self.create_table_name()
        # Create temporary table in duckdb
        with duckdb.connect('bigquery_clone.db') as con:
            # Drop table if it exists
            con.execute(f"DROP TABLE IF EXISTS temp_tables.{table_name}")
            
            # Create temp table from SQL parts
            full_sql = f"""
                CREATE TABLE temp_tables.{table_name} AS 
                SELECT * FROM df_temp
            """
            # print(full_sql)
            try:
                con.execute(full_sql)
            except Exception as e:
                print(f"Error creating temp table: {e}")
                raise e
            if VERBOSE:
                print(f"table {table_name} created")

        # Run main calibration query using temporary table
        sql = SQL_CALIBRE_HEAD.format(table=table_name) + SQL_REACH_BODY + SQL_CALIBRE_FINAL
        # Save SQL query to file for debugging
        if DEBUG:
            with open('calibre_query.sql', 'w') as f:
                f.write(sql)
        # df = pandas_gbq.read_gbq(sql, dialect="standard", use_bqstorage_api=True, progress_bar_type=None)
        # fetch data from duckdb
        if VERBOSE:
            print("fetching data from duckdb")
        with duckdb.connect('bigquery_clone.db') as con:
            df = con.sql(sql).df()
            # print(f"len(df): {len(df):,} rows")
        # print(df)

        # Clean up DuckDB temp table    
        with duckdb.connect('bigquery_clone.db') as con:
            # Drop table if it exists
            if DEBUG:
                if VERBOSE:
                    print(f"temp_tables.{table_name} NOT dropped")
            else:
                con.execute(f"DROP TABLE IF EXISTS temp_tables.{table_name}")
                if VERBOSE:
                    print(f"temp_tables.{table_name} dropped") 

        # Calculate total reach, contacts, reach_sot, and impressions
        reach, contacts, reach_sot, impressions = df[['t_reach', 't_contacts', 't_reach_sot', 't_impressions']].sum().tolist()
        if VERBOSE:
            print(f"reach: {reach:,}, contacts: {contacts:,}, reach_sot: {reach_sot:,}, impressions: {impressions:,}")
        if not not reach:
            response.reach = self.int_nan(reach)
            response.contacts = self.int_nan(contacts)
            response.reach_sot = self.int_nan(reach_sot)
            response.impressions = self.int_nan(impressions)
            if reach == 0:
                print("reach is 0 line 394")
                freq = 0
            else:
                freq = round(contacts / reach, 2)
            response.frequency = freq
            if reach_sot == 0:
                print("reach_sot is 0 line 395")
                freq_sot = 0
            else:
                freq_sot = round(impressions / reach_sot, 2)
            response.frequency_sot = freq_sot

        # Calculate digital reach, contacts, reach_sot, and impressions
        reach, contacts, reach_sot, impressions = df[['d_reach', 'd_contacts', 'd_reach_sot', 'd_impressions']].sum().tolist()
        if not not reach:
            response.digital.reach = self.int_nan(reach)
            response.digital.contacts = self.int_nan(contacts)
            response.digital.reach_sot = self.int_nan(reach_sot)
            response.digital.impressions = self.int_nan(impressions)
            if reach == 0:
                print("reach is 0 line 406")
                freq = 0
            else:
                freq = round(contacts / reach, 2)
            response.digital.frequency = freq
            if reach_sot == 0:
                print("reach_sot is 0 line 407")
                freq_sot = 0
            else:
                freq_sot = round(impressions / reach_sot, 2)
            response.digital.frequency_sot = freq_sot

        # Calculate static reach, contacts, reach_sot, and impressions
        reach, contacts, reach_sot, impressions = df[['s_reach', 's_contacts', 's_reach_sot', 's_impressions']].sum().tolist()
        if not not reach:
            response.static.reach = self.int_nan(reach)
            response.static.contacts = self.int_nan(contacts)
            response.static.reach_sot = self.int_nan(reach_sot)
            response.static.impressions = self.int_nan(impressions)
            if reach == 0:
                print("reach is 0 line 418")
                freq = 0
            else:
                freq = round(contacts / reach, 2)
            response.static.frequency = freq
            if reach_sot == 0:
                print("reach_sot is 0 line 419")
                freq_sot = 0
            else:
                freq_sot = round(impressions / reach_sot, 2)
            response.static.frequency_sot = freq_sot

        # Calculate roadside reach, contacts, reach_sot, and impressions
        reach, contacts, reach_sot, impressions = df[['rs_reach', 'rs_contacts', 'rs_reach_sot', 'rs_impressions']].sum().tolist()
        if not not reach:
            response.roadside.reach = self.int_nan(reach)
            response.roadside.contacts = self.int_nan(contacts)
            response.roadside.reach_sot = self.int_nan(reach_sot)
            response.roadside.impressions = self.int_nan(impressions)
            if reach == 0:
                print("reach is 0 line 430")
                freq = 0
            else:
                freq = round(contacts / reach, 2)
            response.roadside.frequency = freq
            if reach_sot == 0:
                print("reach_sot is 0 line 431")
                freq_sot = 0
            else:
                freq_sot = round(impressions / reach_sot, 2)
            response.roadside.frequency_sot = freq_sot

        # Calculate place-based reach, contacts, reach_sot, and impressions
        reach, contacts, reach_sot, impressions = df[['pb_reach', 'pb_contacts', 'pb_reach_sot', 'pb_impressions']].sum().tolist()
        if not not reach:
            response.placebased.reach = self.int_nan(reach)
            response.placebased.contacts = self.int_nan(contacts)
            response.placebased.reach_sot = self.int_nan(reach_sot)
            response.placebased.impressions = self.int_nan(impressions)
            if reach == 0:
                print("reach is 0 line 442")
                freq = 0
            else:
                freq = round(contacts / reach, 2)
            response.placebased.frequency = freq
            if reach_sot == 0:
                print("reach_sot is 0 line 443")
                freq_sot = 0
            else:
                freq_sot = round(impressions / reach_sot, 2)
            response.placebased.frequency_sot = freq_sot

        # Set target audience summary details
        genders = [g for g in target_genders_dict]
        age_ranges = [a for a in target_ages_dict]
        response.targetAudienceSummary.ageRanges = age_ranges
        response.targetAudienceSummary.genders = genders

        # Create target audience name from genders and age ranges
        name = '/'.join(sorted(genders)) + '-' + '/'.join(sorted(age_ranges))
        response.targetAudienceSummary.name = name

        # Calculate population potential
        with duckdb.connect('bigquery_clone.db') as con:
            df_population_potential = con.sql("SELECT * FROM temp_tables.population_potential").df()
            if not name in df_population_potential['name'].values:
                print(f"name {name} not found in population potential")
                name = 'female/male-15-17/18-24/25-39/40-54/55-64/65'
            response.targetAudienceSummary.populationPotential = df_population_potential[df_population_potential['name'] == name]['population'].iloc[0]

        # Calculate gender ratios
        genders_ratio = 0
        for g in target_genders_dict:
            genders_ratio += df[target_genders_dict[g]]

        # Calculate age ratios
        ages_ratio = 0
        for a in target_ages_dict:
            ages_ratio += df[target_ages_dict[a]]

        # Calculate target audience reach, contacts, reach_sot, and impressions
        reach = (genders_ratio * ages_ratio * df['t_reach']).sum()
        contacts = (genders_ratio * ages_ratio * df['t_contacts']).sum()
        reach_sot = (genders_ratio * ages_ratio * df['t_reach_sot']).sum()
        impressions = (genders_ratio * ages_ratio * df['t_impressions']).sum()

        if not not reach:
            reach = self.int_nan(round(reach))
            if np.isnan(reach):
                reach = 0
            if np.isnan(contacts):
                contacts = 0
            if np.isnan(reach_sot):
                reach_sot = 0
            if np.isnan(impressions):
                impressions = 0
            contacts = self.int_nan(round(contacts))
            reach_sot = self.int_nan(round(reach_sot))
            impressions = self.int_nan(round(impressions))
            if reach == 0:
                print("reach is 0 line 480")
                freq = 0
            else:
                freq = round(contacts / reach, 2)
            response.targetAudienceSummary.reach = reach
            response.targetAudienceSummary.contacts = contacts
            response.targetAudienceSummary.reach_sot = reach_sot
            response.targetAudienceSummary.impressions = impressions
            response.targetAudienceSummary.frequency = freq
            if reach_sot == 0:
                print("reach_sot is 0 line 481")
                freq_sot = 0
            else:
                freq_sot = round(impressions / reach_sot, 2)
            response.targetAudienceSummary.frequency_sot = freq_sot


        # Calculate demographic subsets by age range
        for a in ages_dict:
            obj = CalibreDemographicAudience()
            obj.name = a
            reach = (df[ages_dict[a]] * df['t_reach']).sum()
            contacts = (df[ages_dict[a]] * df['t_contacts']).sum()
            reach_sot = (df[ages_dict[a]] * df['t_reach_sot']).sum()
            impressions = (df[ages_dict[a]] * df['t_impressions']).sum()
            if not not reach:
                reach = self.int_nan(round(reach))
                if np.isnan(contacts):
                    contacts = 0
                if np.isnan(reach_sot):
                    reach_sot = 0
                if np.isnan(impressions):
                    impressions = 0
                contacts = self.int_nan(round(contacts))
                reach_sot = self.int_nan(round(reach_sot))
                impressions = self.int_nan(round(impressions))
                if reach == 0:
                    print("reach is 0 line 501")
                    freq = 0
                else:
                    freq = round(contacts / reach, 2)
                obj.reach = reach
                obj.contacts = contacts 
                obj.reach_sot = reach_sot
                obj.impressions = impressions
                obj.frequency = freq
                if reach_sot == 0:
                    print("reach_sot is 0 line 502")
                    freq_sot = 0
                else:
                    freq_sot = round(impressions / reach_sot, 2)
                obj.frequency_sot = freq_sot
                response.demographicSubsets.ageRanges.append(obj)


        # Calculate demographic subsets by gender
        for g in genders_dict:
            obj = CalibreDemographicAudience()
            obj.name = g
            reach = (df[genders_dict[g]] * df['t_reach']).sum()
            contacts = (df[genders_dict[g]] * df['t_contacts']).sum()
            reach_sot = (df[genders_dict[g]] * df['t_reach_sot']).sum()
            impressions = (df[genders_dict[g]] * df['t_impressions']).sum()
            if not not reach:
                reach = self.int_nan(round(reach))
                if np.isnan(contacts):
                    contacts = 0
                if np.isnan(reach_sot):
                    reach_sot = 0
                if np.isnan(impressions):
                    impressions = 0
                contacts = self.int_nan(round(contacts))
                reach_sot = self.int_nan(round(reach_sot))
                impressions = self.int_nan(round(impressions))
                if reach == 0:
                    print("reach is 0 line 523")
                    freq = 0
                else:
                    freq = round(contacts / reach, 2)
                obj.reach = reach
                obj.contacts = contacts 
                obj.reach_sot = reach_sot
                obj.impressions = impressions
                obj.frequency = freq
                if reach_sot == 0:
                    print("reach_sot is 0 line 524")
                    freq_sot = 0
                else:
                    freq_sot = round(impressions / reach_sot, 2)
                obj.frequency_sot = freq_sot
                response.demographicSubsets.genders.append(obj)

        # Calculate origin FUA
        for g in origin_fua_dict:
            obj = CalibreDemographicAudience()
            obj.name = g
            if type(origin_fua_dict[g]) == list:
                reach = 0
                contacts = 0
                reach_sot = 0
                impressions = 0
                for f in origin_fua_dict[g]:
                    reach += (df[f] * df['t_reach']).sum()
                    contacts += (df[f] * df['t_contacts']).sum()
                    reach_sot += (df[f] * df['t_reach_sot']).sum()
                    impressions += (df[f] * df['t_impressions']).sum()
            else:
                reach = (df[origin_fua_dict[g]] * df['t_reach']).sum()
                contacts = (df[origin_fua_dict[g]] * df['t_contacts']).sum()
                reach_sot = (df[origin_fua_dict[g]] * df['t_reach_sot']).sum()
                impressions = (df[origin_fua_dict[g]] * df['t_impressions']).sum()
            if not not reach:
                reach = self.int_nan(round(reach))
                if np.isnan(contacts):
                    contacts = 0
                if np.isnan(reach_sot):
                    reach_sot = 0
                if np.isnan(impressions):
                    impressions = 0
                contacts = self.int_nan(round(contacts))
                reach_sot = self.int_nan(round(reach_sot))
                impressions = self.int_nan(round(impressions))
                if reach == 0:
                    print("reach is 0 line 551")
                    freq = 0
                else:
                    freq = round(contacts / reach, 2)
                obj.reach = reach
                obj.contacts = contacts 
                obj.reach_sot = reach_sot
                obj.impressions = impressions
                obj.frequency = freq
                if reach_sot == 0:
                    print("reach_sot is 0 line 552")
                    freq_sot = 0
                else:
                    freq_sot = round(impressions / reach_sot, 2)
                obj.frequency_sot = freq_sot
                response.demographicSubsets.originFUA.append(obj)

        # Calculate origin region
        for r in origin_region_dict:
            obj = CalibreDemographicAudience()
            obj.name = r
            reach = 0
            contacts = 0
            reach_sot = 0
            impressions = 0
            for g in origin_region_dict[r]:
                reach += (df[g] * df['t_reach']).sum()
                contacts += (df[g] * df['t_contacts']).sum()
                reach_sot += (df[g] * df['t_reach_sot']).sum()
                impressions += (df[g] * df['t_impressions']).sum()
            if reach > 0:
                reach = self.int_nan(round(reach))
                if np.isnan(contacts):
                    contacts = 0
                if np.isnan(reach_sot):
                    reach_sot = 0
                if np.isnan(impressions):
                    impressions = 0
                contacts = self.int_nan(round(contacts))
                reach_sot = self.int_nan(round(reach_sot))
                impressions = self.int_nan(round(impressions))
                if reach == 0:
                    print("reach is 0 line 575")
                    freq = 0
                else:
                    freq = round(contacts / reach, 2)
                obj.reach = reach
                obj.contacts = contacts 
                obj.reach_sot = reach_sot
                obj.impressions = impressions
                obj.frequency = freq
                if reach_sot == 0:
                    print("reach_sot is 0 line 576")
                    freq_sot = 0
                else:
                    freq_sot = round(impressions / reach_sot, 2)
                obj.frequency_sot = freq_sot
                response.demographicSubsets.originRegion.append(obj)

        # Calculate region scores for each FUA
        for index, row in df.iterrows():
            obj = CalibreRegionScore()
            obj.name = row['fua_name']
            obj.population = self.int_nan(row['population'])
            obj.type = 'FUA'
            reach = row['t_reach']
            contacts = row['t_contacts']
            reach_sot = row['t_reach_sot']
            impressions = row['t_impressions']
            if not not reach:
                if np.isnan(reach):
                    reach = 0
                if np.isnan(contacts):
                    contacts = 0
                if np.isnan(reach_sot):
                    reach_sot = 0
                if np.isnan(impressions):
                    impressions = 0
                obj.reach = self.int_nan(reach)
                obj.contacts = self.int_nan(contacts)
                obj.reach_sot = self.int_nan(reach_sot)
                obj.impressions = self.int_nan(impressions)
                if reach == 0:
                    print("reach is 0 line 600")
                    obj.frequency = 0
                else:
                    obj.frequency = round(contacts / reach, 2)
                if reach_sot == 0:
                    print("reach_sot is 0 line 601")
                    obj.frequency_sot = 0
                else:
                    obj.frequency_sot = round(impressions / reach_sot, 2)
            

            # Calculate target reach for region
            reach = 0
            for g in target_genders_dict:
                reach += row[target_genders_dict[g]] * row['t_reach']
            for a in target_ages_dict:
                reach += row[target_ages_dict[a]] * row['t_reach']
            if np.isnan(reach):
                reach = 0
            obj.targetReach = self.int_nan(round(reach))
            response.regionScores.append(obj)

        # Add panels_in_count_matches_out_count to response
        response.panelsInCountMatchesOutCount = df['panels_in_count_matches_out_count'].iloc[0]
        response.missingPanelIds = df['missing_panel_ids'].iloc[0]

        return response


    def process_panel(self, payload: ModelPanel, params: CalibreAgeGender) -> CalibreResponse:   
        # print(f"payload: {payload}")
        cmax = params.cmax
        cdecay = params.cdecay
        ca = cdecay
        cb = 1 / cmax * np.log((1 + ca - 1 / cmax) / ca)
        cc = cmax

        export = payload.export

        if (not payload.panel_ids) and (not payload.geohashes):
            export = True

        if not payload.coef:
            coef = 'NULL'
        else:
            coef = payload.coef
        
        panel_id_condition = ""
        if not not payload.panel_ids:
            panel_id_condition = "\t\tWHERE panel_id IN ({})".format(', '.join([str(pid) for pid in payload.panel_ids]))

        cols = None
        if not not payload.geohashes:
            raise Exception('Geohashes not supported yet')
            # ghs_str = "['" + "', '".join(payload.geohashes) + "']"
            # cols = ['reach', 'contacts']
            # sql = SQL_PANEL.format(
            #     from_date=payload.start_date, 
            #     to_date=payload.end_date,
            #     coef=coef, 
            #     ca=ca,
            #     cb=cb,
            #     cc=cc, 
            #     ghs=ghs_str
            # )
        else:
            sql = SQL_BULK_PANEL_HEAD.format(
                panel_id_condition=panel_id_condition, 
                from_date=payload.start_date, to_date=payload.end_date, 
            ) + SQL_REACH_BODY + SQL_BULK_PANEL_FINAL
        # print(f"sql: {sql}")
        dt = datetime.datetime.now(pytz.timezone('Pacific/Auckland'))
        # df = pandas_gbq.read_gbq(sql, dialect="standard")
        if VERBOSE:
            print("fetching data from duckdb")
        with duckdb.connect('bigquery_clone.db') as con:
            df = con.sql(sql).df()
        # print(f"export: {export}, df: {len(df):,} rows")
        if export:
            file_name = 'panels_rc_{}_{}_{}.csv'.format(
                payload.start_date,
                payload.end_date,
                dt.strftime('%H%M%S')
            )
            
            year = str(dt.year)
            month = str(dt.month)
            day = str(dt.day)
            # file_path = 'gs://' + os.path.join(payload.bucket, year, month, day, file_name)
            file_path = 'data/' + file_name
            df.to_csv(file_path, index=False)
            return {'message':'results exported!', 'file_path': file_path}
        
        if not cols:
            res = df.to_dict(orient='records')
        else:
            res = df[cols].to_dict(orient='records')

        return {'results': res}