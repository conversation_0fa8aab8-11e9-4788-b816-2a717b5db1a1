# Log

20250630 1215 Copied geo-api-azure requirements.txt fixing duckdb==1.1.3

20250630 1212 Replaced code with geo-api-local app.py, geo_functions_py and constants.py before adding mortalportal.fact_all_panels_impressions_daily

20250513 1049 Started work on SOT Share of Time version.

20250131 1336 Redeployed.
- Start Docker
- gcauth_init
- r (start Cursor)
- deploy with Google Cloud extension Cloud Run > geo-api (small cloud with up arrow icon)

20250120 1316 Redeployed. See [Confluence](https://reachmedianz.atlassian.net/wiki/spaces/DS/pages/**********/geo-api+deployment)

20250115 1405 API has 3 endpoints:

- /health
- /campaigns
- /panels

20250115 1402 Began re-work to use simplified model. 

conda activate geo_api
cd /home/<USER>/daily/20250115/geo-api
flask run

# REACH audience measurement API

This API is deployed to a GCP Cloud Run. It can be built locally using Docker and deployed to the remote instance. 

In the future, we need to create a proper deployment pipeline. 

A sample request is as below:

```json
{
    "name":"QCTPN_D",
    "advertiserName":"AdvertiserName",
    "tags":null,
    "flights":[
        {
            "startDate":"2024-07-01T00:00:00+00:00",
            "endDate":"2024-07-07T00:00:00+00:00",
            "clientPanelIds":[
                "2984",
                "10279",
                "10166"
            ]
        }, {
            "startDate":"2024-07-08T00:00:00+00:00",
            "endDate":"2024-07-14T00:00:00+00:00",
            "clientPanelIds":[
                "2984"
            ]
        }
    ]
}
```
