# pylint: skip-file
# flake8: noqa
# 20250214 1535 renamed audience to contacts, avg_freq to frequency and removed audiencePotential

from typing import List, Optional
from pydantic import BaseModel, field_validator
from pydantic_core.core_schema import FieldValidationInfo
import datetime
import time


class CalibreAgeGender(BaseModel):
    ageRanges: Optional[str] = None
    genders: Optional[str] = None
    traffic: bool = True
    pdist: float = 50
    pratio: float = 0.05
    poratio: float = 0.9
    pbcoef: float = 10
    cmax: float = 2
    cdecay: float = 0.8
    ttl: int = 3

    @field_validator('ageRanges')
    @classmethod
    def check_age_format(cls, v):
        vals = v.split(',')
        expected_vals = ['15-17', '18-24', '25-39', '40-54', '55-64', '65']
        for val in vals:
            if val not in expected_vals:
                raise ValueError('ageRanges should be a collection of 15-17,18-24,25-39,40-54,55-64,65')
        return ','.join(vals)
    
    @field_validator('genders')
    @classmethod
    def check_gender_format(cls, v):
        vals = v.split(',')
        expected_vals = ['male', 'female']
        for val in vals:
            if val not in expected_vals:
                raise ValueError('genders should be a collection of male,female')
        return ','.join(vals)
    

class CalibreFlight(BaseModel):
    startDate: str
    endDate: str
    clientPanelIds: List[str]

    @classmethod
    def date_extraction(cls, v):
        patterns = ['%Y-%m-%d', 'ISO']
        for pattern in patterns:
            try:
                if pattern == 'ISO':
                    dt = datetime.datetime.fromisoformat(v)
                else:
                    dt = datetime.datetime.strptime(v, pattern)
                return datetime.datetime.strftime(dt, patterns[0])
            except:
                pass
        raise ValueError('startDate or endDate has a bad format!')

    @field_validator('startDate')
    @classmethod
    def check_start_date_format(cls, v, info: FieldValidationInfo):
        dt_start = cls.date_extraction(v)
        if 'endDate' in info.data:
            dt_end = cls.date_extraction(info.data['endDate'])
            if dt_start > dt_end:
                raise ValueError('startDate cannot be larger than the endDate!')
        return dt_start
    
    @field_validator('endDate')
    @classmethod
    def check_end_date_format(cls, v, info: FieldValidationInfo):
        dt_end = cls.date_extraction(v)
        if 'startDate' in info.data:
            dt_start = cls.date_extraction(info.data['startDate'])
            if dt_start > dt_end:
                raise ValueError('startDate cannot be larger than the endDate!')
        return dt_end
    
    @field_validator('clientPanelIds')
    @classmethod
    def check_panelid_format(cls, v):
        for val in v:
            if not val.isnumeric():
                raise ValueError('clientPanelIds should be an array of panel ids with numeric values!')
        return v


class CalibreCampaign(BaseModel):
    name: Optional[str] = None
    advertiserName:Optional[str] = None
    tags: Optional[str] = None
    flights: List[CalibreFlight]


class CalibreAudience(BaseModel):
    reach: int = 0
    frequency: float = 0
    contacts: int = 0
    reach_sot: int = 0
    frequency_sot: float = 0
    impressions: int = 0


class CalibreDemographicAudience(BaseModel):
    reach: int = 0
    frequency: float = 0
    contacts: int = 0
    reach_sot: int = 0
    frequency_sot: float = 0
    impressions: int = 0
    name: Optional[str] = None


class CalibreTargetAudience(BaseModel):
    reach: int = 0
    frequency: float = 0
    contacts: int = 0
    reach_sot: int = 0
    frequency_sot: float = 0
    impressions: int = 0
    name: Optional[str] = None
    genders: Optional[List[str]] = None
    ageRanges: Optional[List[str]] = None
    populationPotential: int = 0


class CalibreRegionScore(BaseModel):
    reach: int = 0
    frequency: float = 0
    contacts: int = 0
    reach_sot: int = 0
    frequency_sot: float = 0
    impressions: int = 0
    population: int = 0
    name: Optional[str] = None
    targetReach: int = 0
    type: Optional[str] = None


class CalibreDemographicSubset(BaseModel):
    genders: List[CalibreDemographicAudience] = []
    ageRanges: List[CalibreDemographicAudience] = []
    originFUA: List[CalibreDemographicAudience] = []
    originRegion: List[CalibreDemographicAudience] = []


class CalibreResponse(BaseModel):
    campaignId: Optional[str] = None
    reach: Optional[int] = None
    frequency: Optional[float] = None
    contacts: Optional[int] = None
    reach_sot: Optional[int] = None
    frequency_sot: Optional[float] = None
    impressions: Optional[int] = None
    roadside: CalibreAudience = CalibreAudience()
    placebased: CalibreAudience = CalibreAudience()
    digital: CalibreAudience = CalibreAudience()
    static: CalibreAudience = CalibreAudience()
    targetAudienceSummary: CalibreTargetAudience = CalibreTargetAudience()
    regionScores: List[CalibreRegionScore] = []
    demographicSubsets: CalibreDemographicSubset = CalibreDemographicSubset()
    panelsInCountMatchesOutCount: bool = False
    missingPanelIds: Optional[str] = None
    as_at: str = time.strftime("%Y%m%d %H:%M:%S")


class ModelPanel(BaseModel):
    panel_ids: Optional[List[int]] = None
    coef: Optional[float] = None
    geohashes: Optional[List[str]] = None
    start_date: str
    end_date: str
    export: bool = False
    bucket: str = 'calibre_tests'